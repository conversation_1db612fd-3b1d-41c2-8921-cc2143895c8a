"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    // 🔧 统一的定时器管理 - 只保留一个定时器用于自动保存\n    const saveTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 统一的定时器清理函数\n    const clearSaveTimer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (saveTimeoutRef.current) {\n            clearTimeout(saveTimeoutRef.current);\n            saveTimeoutRef.current = undefined;\n            console.log(\"\\uD83D\\uDD12 清除保存定时器\");\n        }\n    }, []);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 🔧 文件切换时清除所有定时器\n        clearSaveTimer();\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        // 🔧 视图切换时清除所有定时器\n        clearSaveTimer();\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        if (!settings.autoSave || !onContentChange) return;\n        clearSaveTimer();\n        saveTimeoutRef.current = setTimeout(()=>{\n            onContentChange(content);\n        }, settings.autoSaveDelay);\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        onContentChange,\n        clearSaveTimer\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 🔧 验证当前文件状态，防止跨文件保存\n        if (!file || !isMindMapMode) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 将思维导图数据转换为Markdown格式并保存\n        if (data && onContentChange) {\n            const markdownContent = convertMindMapToMarkdown(data);\n            handleSave(markdownContent);\n            console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并自动保存\");\n        }\n    }, [\n        file,\n        isMindMapMode,\n        onContentChange,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (autoSaveTimeoutRef.current) {\n                clearTimeout(autoSaveTimeoutRef.current);\n            }\n            if (settingsDebounceRef.current) {\n                clearTimeout(settingsDebounceRef.current);\n            }\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, []);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 631,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 630,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 826,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 817,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 697,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 647,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 855,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 864,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 873,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 876,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 872,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 892,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 888,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 905,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 922,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 939,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 956,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 852,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 851,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 983,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 982,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 986,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 981,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 980,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 992,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 994,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 990,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1004,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 978,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1020,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1018,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1022,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1017,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1016,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1029,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1030,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1039,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1031,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1027,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1026,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1057,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1074,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1075,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1076,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1073,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1072,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1014,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1094,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1095,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1093,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1097,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1092,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1091,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1084,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1083,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 645,\n        columnNumber: 5\n    }, undefined);\n}, \"PG2CD78xuZgttSDmLmbn9ucF1P0=\")), \"PG2CD78xuZgttSDmLmbn9ucF1P0=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});