/**
 * 文件树事件管理服务
 * 管理文件树刷新事件的订阅和通知机制
 * 基于PersonaService和ChatHistoryService的事件模式设计
 */

export class FileTreeEventService {
  private static instance: FileTreeEventService;
  
  // 文件树刷新事件监听器
  private refreshListeners: (() => void)[] = [];
  
  // 文件内容更新事件监听器
  private fileContentUpdateListeners: ((fileId: string) => void)[] = [];

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  /**
   * 获取文件树事件服务单例
   */
  public static getInstance(): FileTreeEventService {
    if (!FileTreeEventService.instance) {
      FileTreeEventService.instance = new FileTreeEventService();
    }
    return FileTreeEventService.instance;
  }

  /**
   * 订阅文件树刷新事件
   */
  public subscribeRefresh(listener: () => void): void {
    this.refreshListeners.push(listener);
    console.log('✅ 文件树刷新监听器已添加，当前监听器数量:', this.refreshListeners.length);
  }

  /**
   * 取消订阅文件树刷新事件
   */
  public unsubscribeRefresh(listener: () => void): void {
    this.refreshListeners = this.refreshListeners.filter(l => l !== listener);
    console.log('✅ 文件树刷新监听器已移除，当前监听器数量:', this.refreshListeners.length);
  }

  /**
   * 触发文件树刷新事件
   */
  public triggerRefresh(): void {
    console.log('🔄 触发文件树刷新事件');
    this.notifyRefreshListeners();
  }

  /**
   * 通知所有监听器文件树需要刷新 - 增强版本，带错误处理和状态验证
   */
  private notifyRefreshListeners(): void {
    const listenerCount = this.refreshListeners.length;
    console.log('🔔 通知文件树刷新:', '监听器数量:', listenerCount);
    
    if (listenerCount === 0) {
      console.warn('⚠️ 没有文件树刷新监听器，通知将被忽略');
      return;
    }
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const listener of this.refreshListeners) {
      try {
        listener();
        successCount++;
      } catch (error) {
        errorCount++;
        console.error('❌ 文件树刷新监听器执行失败:', error);
        console.error('❌ 监听器函数:', listener.toString().substring(0, 100) + '...');
      }
    }
    
    if (errorCount > 0) {
      console.warn(`⚠️ 文件树刷新通知完成: 成功 ${successCount}/${listenerCount}, 失败 ${errorCount}`);
    } else {
      console.log(`✅ 文件树刷新通知成功: ${successCount}/${listenerCount} 个监听器`);
    }
  }

  /**
   * 获取当前刷新监听器数量
   */
  public getRefreshListenerCount(): number {
    return this.refreshListeners.length;
  }

  /**
   * 订阅文件内容更新事件
   */
  public subscribeFileContentUpdate(listener: (fileId: string) => void): void {
    this.fileContentUpdateListeners.push(listener);
    console.log('✅ 文件内容更新监听器已添加，当前监听器数量:', this.fileContentUpdateListeners.length);
  }

  /**
   * 取消订阅文件内容更新事件
   */
  public unsubscribeFileContentUpdate(listener: (fileId: string) => void): void {
    this.fileContentUpdateListeners = this.fileContentUpdateListeners.filter(l => l !== listener);
    console.log('✅ 文件内容更新监听器已移除，当前监听器数量:', this.fileContentUpdateListeners.length);
  }

  /**
   * 触发文件内容更新事件
   */
  public triggerFileContentUpdate(fileId: string): void {
    console.log('🔄 触发文件内容更新事件:', fileId);
    this.notifyFileContentUpdateListeners(fileId);
  }

  /**
   * 通知所有监听器文件内容已更新
   */
  private notifyFileContentUpdateListeners(fileId: string): void {
    const listenerCount = this.fileContentUpdateListeners.length;
    console.log('🔔 通知文件内容更新:', '文件ID:', fileId, '监听器数量:', listenerCount);
    
    if (listenerCount === 0) {
      console.warn('⚠️ 没有文件内容更新监听器，通知将被忽略');
      return;
    }
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const listener of this.fileContentUpdateListeners) {
      try {
        listener(fileId);
        successCount++;
      } catch (error) {
        errorCount++;
        console.error('❌ 文件内容更新监听器执行失败:', error);
        console.error('❌ 监听器函数:', listener.toString().substring(0, 100) + '...');
      }
    }
    
    if (errorCount > 0) {
      console.warn(`⚠️ 文件内容更新通知完成: 成功 ${successCount}/${listenerCount}, 失败 ${errorCount}`);
    } else {
      console.log(`✅ 文件内容更新通知成功: ${successCount}/${listenerCount} 个监听器`);
    }
  }

  /**
   * 清除所有监听器（用于测试或重置）
   */
  public clearAllListeners(): void {
    const refreshCount = this.refreshListeners.length;
    const contentCount = this.fileContentUpdateListeners.length;
    this.refreshListeners = [];
    this.fileContentUpdateListeners = [];
    console.log(`🧹 已清除所有监听器: 文件树刷新 ${refreshCount} 个, 文件内容更新 ${contentCount} 个`);
  }
}