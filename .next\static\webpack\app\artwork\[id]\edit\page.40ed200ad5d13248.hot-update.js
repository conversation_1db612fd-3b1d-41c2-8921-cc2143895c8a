"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const autoSaveTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const settingsDebounceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        // 防抖自动保存\n        if (settings.autoSave && onContentChange) {\n            if (autoSaveTimeoutRef.current) {\n                clearTimeout(autoSaveTimeoutRef.current);\n            }\n            autoSaveTimeoutRef.current = setTimeout(()=>{\n                onContentChange(content);\n            }, settings.autoSaveDelay);\n        }\n    };\n    // 处理思维导图数据变更\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 将思维导图数据转换为Markdown格式并保存\n        if (data && onContentChange) {\n            const markdownContent = convertMindMapToMarkdown(data);\n            // 防抖自动保存\n            if (autoSaveTimeoutRef.current) {\n                clearTimeout(autoSaveTimeoutRef.current);\n            }\n            autoSaveTimeoutRef.current = setTimeout(()=>{\n                onContentChange(markdownContent);\n                console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并自动保存\");\n            }, settings.autoSaveDelay);\n        }\n    }, [\n        onContentChange,\n        settings.autoSave,\n        settings.autoSaveDelay,\n        convertMindMapToMarkdown\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 防抖处理字符数变更\n    const handleWordWrapColumnChange = (value)=>{\n        // 清除之前的防抖定时器\n        if (settingsDebounceRef.current) {\n            clearTimeout(settingsDebounceRef.current);\n        }\n        // 设置新的防抖定时器\n        settingsDebounceRef.current = setTimeout(()=>{\n            handleSettingsChange({\n                wordWrapColumn: value\n            });\n        }, 300) // 300ms防抖延迟，平衡响应性和性能\n        ;\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (autoSaveTimeoutRef.current) {\n                clearTimeout(autoSaveTimeoutRef.current);\n            }\n            if (settingsDebounceRef.current) {\n                clearTimeout(settingsDebounceRef.current);\n            }\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, []);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 626,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 625,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 687,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 721,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 827,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 821,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 642,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 871,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 879,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 867,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 884,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 896,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 883,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 909,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 910,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 911,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 918,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 929,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 917,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 934,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 951,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 847,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 846,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 979,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 976,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 975,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 987,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 989,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 990,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 986,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 985,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 999,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 973,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1014,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1013,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1017,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1012,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1011,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1023,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1025,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1027,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1026,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1022,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1021,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1052,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1069,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1068,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1067,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1009,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1089,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1088,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1092,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1087,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1086,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1079,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1078,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 640,\n        columnNumber: 5\n    }, undefined);\n}, \"eMLkdTC7Lqb5OA/94M7ubwRGtZo=\")), \"eMLkdTC7Lqb5OA/94M7ubwRGtZo=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/FileTreePanel/index.tsx":
/*!************************************************!*\
  !*** ./src/components/FileTreePanel/index.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileTreePanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_fileTreeService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/fileTreeService */ \"(app-pages-browser)/./src/services/fileTreeService.ts\");\n/* harmony import */ var _services_fileTreeEventService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/fileTreeEventService */ \"(app-pages-browser)/./src/services/fileTreeEventService.ts\");\n/* harmony import */ var _services_fileAssociationService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/fileAssociationService */ \"(app-pages-browser)/./src/services/fileAssociationService.ts\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_SVGDecorations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/SVGDecorations */ \"(app-pages-browser)/./src/components/SVGDecorations/index.tsx\");\n/* harmony import */ var _components_common_ContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/common/ContextMenu */ \"(app-pages-browser)/./src/components/common/ContextMenu.tsx\");\n/* harmony import */ var _components_PersonaManager_ConfirmDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/PersonaManager/ConfirmDialog */ \"(app-pages-browser)/./src/components/PersonaManager/ConfirmDialog.tsx\");\n/* harmony import */ var _components_MessageTokenViewer_MessageTokenStatusIndicator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/MessageTokenViewer/MessageTokenStatusIndicator */ \"(app-pages-browser)/./src/components/MessageTokenViewer/MessageTokenStatusIndicator.tsx\");\n/**\r\n * 文件树面板组件\r\n * IDE风格的文件管理界面，支持拖拽和优化图标\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// 优化的文件夹图标组件 - 更明显的金色设计\nconst FolderIcon = (param)=>{\n    let { isOpen = false, size = 16 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: isOpen ? // 展开状态 - 打开的文件夹\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M2 7V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V9C22 7.89543 21.1046 7 20 7H11L9 5H4C2.89543 5 2 5.89543 2 7Z\",\n                    fill: \"#F0E68C\",\n                    stroke: \"#D4AF37\",\n                    strokeWidth: \"1.2\",\n                    style: {\n                        filter: \"drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3))\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M4 9H20V18H4V9Z\",\n                    fill: \"rgba(240, 230, 140, 0.4)\",\n                    stroke: \"#B8860B\",\n                    strokeWidth: \"0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9 5H11L13 7H20\",\n                    stroke: \"#D4AF37\",\n                    strokeWidth: \"1\",\n                    fill: \"none\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                    cx: \"12\",\n                    cy: \"13\",\n                    rx: \"6\",\n                    ry: \"2\",\n                    fill: \"rgba(255, 255, 255, 0.2)\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined) : // 折叠状态 - 关闭的文件夹\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M2 7V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V9C22 7.89543 21.1046 7 20 7H11L9 5H4C2.89543 5 2 5.89543 2 7Z\",\n                    fill: \"#D4AF37\",\n                    stroke: \"#B8860B\",\n                    strokeWidth: \"1.2\",\n                    style: {\n                        filter: \"drop-shadow(0 2px 4px rgba(212, 175, 55, 0.4))\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9 5H11L13 7H20\",\n                    stroke: \"#F0E68C\",\n                    strokeWidth: \"1\",\n                    fill: \"none\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"8\",\n                    cy: \"13\",\n                    r: \"1\",\n                    fill: \"#F0E68C\",\n                    opacity: \"0.8\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"16\",\n                    cy: \"13\",\n                    r: \"1\",\n                    fill: \"#F0E68C\",\n                    opacity: \"0.8\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                    cx: \"12\",\n                    cy: \"11\",\n                    rx: \"5\",\n                    ry: \"1.5\",\n                    fill: \"rgba(255, 255, 255, 0.3)\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\n};\n_c = FolderIcon;\n// 优化的文件图标组件 - 与文件夹明显区分\nconst FileIcon = (param)=>{\n    let { type, fileName = \"\", size = 16 } = param;\n    // 使用fileTypeUtils进行统一文件类型检测\n    const detectedType = type || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_5__.getFileTypeFromName)(fileName);\n    const isChartFile = detectedType === \"chart\";\n    const isXMindFile = detectedType === \"xmind\";\n    const isMindMapFile = detectedType === \"mindmap\";\n    if (isMindMapFile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: size,\n            height: size,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z\",\n                    fill: \"rgba(16, 185, 129, 0.1)\",\n                    stroke: \"#10B981\",\n                    strokeWidth: \"1.2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    style: {\n                        filter: \"drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3))\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M14 2V8H20\",\n                    stroke: \"#10B981\",\n                    strokeWidth: \"1.2\",\n                    fill: \"rgba(16, 185, 129, 0.1)\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"2.5\",\n                            fill: \"rgba(16, 185, 129, 0.3)\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"7\",\n                            cy: \"9\",\n                            r: \"1.5\",\n                            fill: \"rgba(16, 185, 129, 0.2)\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"17\",\n                            cy: \"9\",\n                            r: \"1.5\",\n                            fill: \"rgba(16, 185, 129, 0.2)\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"7\",\n                            cy: \"15\",\n                            r: \"1.5\",\n                            fill: \"rgba(16, 185, 129, 0.2)\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"17\",\n                            cy: \"15\",\n                            r: \"1.5\",\n                            fill: \"rgba(16, 185, 129, 0.2)\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"10\",\n                            y1: \"10.5\",\n                            x2: \"8.5\",\n                            y2: \"9.5\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"14\",\n                            y1: \"10.5\",\n                            x2: \"15.5\",\n                            y2: \"9.5\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"10\",\n                            y1: \"13.5\",\n                            x2: \"8.5\",\n                            y2: \"14.5\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"14\",\n                            y1: \"13.5\",\n                            x2: \"15.5\",\n                            y2: \"14.5\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"5\",\n                            cy: \"7\",\n                            r: \"0.8\",\n                            fill: \"#10B981\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"19\",\n                            cy: \"7\",\n                            r: \"0.8\",\n                            fill: \"#10B981\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"5\",\n                            cy: \"17\",\n                            r: \"0.8\",\n                            fill: \"#10B981\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"19\",\n                            cy: \"17\",\n                            r: \"0.8\",\n                            fill: \"#10B981\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"7\",\n                            y1: \"8\",\n                            x2: \"5.8\",\n                            y2: \"7.2\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"0.6\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"17\",\n                            y1: \"8\",\n                            x2: \"18.2\",\n                            y2: \"7.2\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"0.6\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"7\",\n                            y1: \"16\",\n                            x2: \"5.8\",\n                            y2: \"16.8\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"0.6\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"17\",\n                            y1: \"16\",\n                            x2: \"18.2\",\n                            y2: \"16.8\",\n                            stroke: \"#10B981\",\n                            strokeWidth: \"0.6\",\n                            opacity: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isXMindFile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: size,\n            height: size,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z\",\n                    fill: \"rgba(147, 51, 234, 0.1)\",\n                    stroke: \"#9333EA\",\n                    strokeWidth: \"1.2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    style: {\n                        filter: \"drop-shadow(0 1px 2px rgba(147, 51, 234, 0.3))\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M14 2V8H20\",\n                    stroke: \"#9333EA\",\n                    strokeWidth: \"1.2\",\n                    fill: \"rgba(147, 51, 234, 0.1)\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"2\",\n                            fill: \"rgba(147, 51, 234, 0.3)\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"8\",\n                            cy: \"10\",\n                            r: \"1.2\",\n                            fill: \"rgba(147, 51, 234, 0.2)\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"16\",\n                            cy: \"10\",\n                            r: \"1.2\",\n                            fill: \"rgba(147, 51, 234, 0.2)\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"8\",\n                            cy: \"14\",\n                            r: \"1.2\",\n                            fill: \"rgba(147, 51, 234, 0.2)\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"16\",\n                            cy: \"14\",\n                            r: \"1.2\",\n                            fill: \"rgba(147, 51, 234, 0.2)\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"10.5\",\n                            y1: \"11\",\n                            x2: \"9.2\",\n                            y2: \"10.2\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"13.5\",\n                            y1: \"11\",\n                            x2: \"14.8\",\n                            y2: \"10.2\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"10.5\",\n                            y1: \"13\",\n                            x2: \"9.2\",\n                            y2: \"13.8\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"13.5\",\n                            y1: \"13\",\n                            x2: \"14.8\",\n                            y2: \"13.8\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isChartFile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: size,\n            height: size,\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z\",\n                    fill: \"rgba(59, 130, 246, 0.1)\",\n                    stroke: \"#3B82F6\",\n                    strokeWidth: \"1.2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    style: {\n                        filter: \"drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3))\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M14 2V8H20\",\n                    stroke: \"#3B82F6\",\n                    strokeWidth: \"1.2\",\n                    fill: \"rgba(59, 130, 246, 0.1)\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                            x: \"7\",\n                            y: \"10\",\n                            width: \"10\",\n                            height: \"8\",\n                            rx: \"1\",\n                            fill: \"rgba(59, 130, 246, 0.2)\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"9\",\n                            cy: \"12\",\n                            r: \"1\",\n                            fill: \"#3B82F6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"15\",\n                            cy: \"12\",\n                            r: \"1\",\n                            fill: \"#3B82F6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"12\",\n                            cy: \"16\",\n                            r: \"1\",\n                            fill: \"#3B82F6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"9\",\n                            y1: \"12\",\n                            x2: \"15\",\n                            y2: \"12\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"12\",\n                            y1: \"12\",\n                            x2: \"12\",\n                            y2: \"16\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 191,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z\",\n                fill: \"rgba(255, 255, 255, 0.05)\",\n                stroke: \"#9CA3AF\",\n                strokeWidth: \"1.2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                style: {\n                    filter: \"drop-shadow(0 1px 2px rgba(156, 163, 175, 0.2))\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M14 2V8H20\",\n                stroke: \"#9CA3AF\",\n                strokeWidth: \"1.2\",\n                fill: \"rgba(156, 163, 175, 0.1)\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, undefined),\n            detectedType === \"markdown\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        x: \"8\",\n                        y: \"14\",\n                        width: \"8\",\n                        height: \"4\",\n                        rx: \"1\",\n                        fill: \"rgba(245, 158, 11, 0.2)\",\n                        stroke: \"#F59E0B\",\n                        strokeWidth: \"0.5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                        x: \"12\",\n                        y: \"16.5\",\n                        textAnchor: \"middle\",\n                        fontSize: \"6\",\n                        fill: \"#F59E0B\",\n                        fontWeight: \"bold\",\n                        children: \"MD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                opacity: \"0.3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                        x1: \"7\",\n                        y1: \"10\",\n                        x2: \"13\",\n                        y2: \"10\",\n                        stroke: \"#9CA3AF\",\n                        strokeWidth: \"0.5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                        x1: \"7\",\n                        y1: \"12\",\n                        x2: \"15\",\n                        y2: \"12\",\n                        stroke: \"#9CA3AF\",\n                        strokeWidth: \"0.5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                        x1: \"7\",\n                        y1: \"14\",\n                        x2: \"11\",\n                        y2: \"14\",\n                        stroke: \"#9CA3AF\",\n                        strokeWidth: \"0.5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = FileIcon;\n// 作品根节点图标组件 - 特殊的作品图标\nconst ArtworkIcon = (param)=>{\n    let { size = 18 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 4Z\",\n                    fill: \"rgba(139, 69, 19, 0.8)\",\n                    stroke: \"#8B4513\",\n                    strokeWidth: \"1.5\",\n                    style: {\n                        filter: \"drop-shadow(0 3px 6px rgba(139, 69, 19, 0.4))\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M4 4V20C4 21.1046 4.89543 22 6 22\",\n                    stroke: \"#D2691E\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                    opacity: \"0.7\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"7\",\n                            y1: \"8\",\n                            x2: \"17\",\n                            y2: \"8\",\n                            stroke: \"#F4A460\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"7\",\n                            y1: \"11\",\n                            x2: \"15\",\n                            y2: \"11\",\n                            stroke: \"#F4A460\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"7\",\n                            y1: \"14\",\n                            x2: \"16\",\n                            y2: \"14\",\n                            stroke: \"#F4A460\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"8\",\n                    cy: \"18\",\n                    r: \"1\",\n                    fill: \"#DAA520\",\n                    opacity: \"0.8\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"16\",\n                    cy: \"18\",\n                    r: \"1\",\n                    fill: \"#DAA520\",\n                    opacity: \"0.8\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                    cx: \"12\",\n                    cy: \"10\",\n                    rx: \"6\",\n                    ry: \"2\",\n                    fill: \"rgba(255, 255, 255, 0.2)\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 271,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n        lineNumber: 270,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = ArtworkIcon;\n// 文件树项组件\nconst FileTreeItem = (param)=>{\n    let { node, level, isSelected, onSelect, onToggle, onContextMenu, onRename, expandedNodes, onFileMove, draggedNodeId, dragOverNodeId, onDragStart, onDragEnd, onDragOver, onDragLeave, messageTokenStatus, isGeneratingTokens } = param;\n    _s();\n    const [isRenaming, setIsRenaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(node.name);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const expandTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const isExpanded = expandedNodes.has(node.id);\n    const hasChildren = node.children && node.children.length > 0;\n    const isDragging = draggedNodeId === node.id;\n    const isDragOver = dragOverNodeId === node.id;\n    // 处理重命名\n    const handleRename = ()=>{\n        if (renamingValue.trim() && renamingValue !== node.name) {\n            onRename(node.id, renamingValue.trim());\n        }\n        setIsRenaming(false);\n        setRenamingValue(node.name);\n    };\n    // 开始重命名\n    const startRename = ()=>{\n        setIsRenaming(true);\n        setRenamingValue(node.name);\n        setTimeout(()=>{\n            var _inputRef_current, _inputRef_current1;\n            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n            (_inputRef_current1 = inputRef.current) === null || _inputRef_current1 === void 0 ? void 0 : _inputRef_current1.select();\n        }, 0);\n    };\n    // 处理键盘事件\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            handleRename();\n        } else if (e.key === \"Escape\") {\n            setIsRenaming(false);\n            setRenamingValue(node.name);\n        }\n    };\n    // 清理定时器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (expandTimeoutRef.current) {\n                clearTimeout(expandTimeoutRef.current);\n            }\n        };\n    }, []);\n    // 拖拽事件处理\n    const handleDragStart = (e)=>{\n        if (node.name === \"root\") return; // 根节点不能拖拽\n        onDragStart(node.id);\n        e.dataTransfer.setData(\"text/plain\", node.id);\n        e.dataTransfer.effectAllowed = \"move\";\n    };\n    const handleDragEnd = ()=>{\n        onDragEnd();\n        if (expandTimeoutRef.current) {\n            clearTimeout(expandTimeoutRef.current);\n        }\n    };\n    const handleDragOver = (e)=>{\n        if (node.type === \"folder\" && !isDragging) {\n            e.preventDefault();\n            e.dataTransfer.dropEffect = \"move\";\n            onDragOver(node.id);\n            // 悬停1秒后自动展开文件夹\n            if (!isExpanded) {\n                if (expandTimeoutRef.current) {\n                    clearTimeout(expandTimeoutRef.current);\n                }\n                expandTimeoutRef.current = setTimeout(()=>{\n                    onToggle(node.id);\n                }, 1000);\n            }\n        }\n    };\n    const handleDragLeave = (e)=>{\n        // 只有当鼠标真正离开元素时才触发\n        const rect = e.currentTarget.getBoundingClientRect();\n        const x = e.clientX;\n        const y = e.clientY;\n        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {\n            onDragLeave();\n            if (expandTimeoutRef.current) {\n                clearTimeout(expandTimeoutRef.current);\n            }\n        }\n    };\n    const handleDrop = async (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        onDragLeave();\n        if (expandTimeoutRef.current) {\n            clearTimeout(expandTimeoutRef.current);\n        }\n        if (node.type !== \"folder\") return;\n        const draggedFileId = e.dataTransfer.getData(\"text/plain\");\n        if (draggedFileId && draggedFileId !== node.id) {\n            const success = await onFileMove(draggedFileId, node.id);\n            if (success) {\n                // 确保目标文件夹展开以显示移动的文件\n                if (!isExpanded) {\n                    onToggle(node.id);\n                }\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          flex items-center gap-2 px-3 py-2 cursor-pointer group\\n          hover:bg-amber-500/10 transition-all duration-200\\n          \".concat(isSelected ? \"bg-gradient-to-r from-amber-500/20 to-amber-600/10 border-l-3 border-amber-500 shadow-lg shadow-amber-500/10\" : \"\", \"\\n          \").concat(isDragging ? \"opacity-50 scale-95\" : \"\", \"\\n          \").concat(isDragOver ? \"bg-amber-500/20 border-2 border-amber-500/50 border-dashed\" : \"\", \"\\n          rounded-r-lg mx-1 my-0.5\\n        \"),\n                style: {\n                    paddingLeft: \"\".concat(level * 20 + 12, \"px\")\n                },\n                draggable: node.name !== \"root\",\n                onClick: ()=>onSelect(node),\n                onContextMenu: (e)=>onContextMenu(e, node),\n                onDragStart: handleDragStart,\n                onDragEnd: handleDragEnd,\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                children: [\n                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"p-1 hover:bg-amber-500/20 rounded-md transition-all duration-200 group-hover:bg-amber-500/15\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            onToggle(node.id);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"12\",\n                            height: \"12\",\n                            viewBox: \"0 0 12 12\",\n                            className: \"transition-transform duration-200 \".concat(isExpanded ? \"rotate-90\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M4 2l4 4-4 4\",\n                                fill: \"none\",\n                                stroke: \"#F59E0B\",\n                                strokeWidth: \"2\",\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                style: {\n                                    filter: \"drop-shadow(0 0 2px rgba(245, 158, 11, 0.4))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: node.type === \"folder\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FolderIcon, {\n                            isOpen: isExpanded\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileIcon, {\n                            fileName: node.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex items-center gap-2\",\n                        children: isRenaming ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ref: inputRef,\n                            type: \"text\",\n                            value: renamingValue,\n                            onChange: (e)=>setRenamingValue(e.target.value),\n                            onBlur: handleRename,\n                            onKeyDown: handleKeyDown,\n                            className: \"flex-1 px-2 py-1 text-sm bg-gray-800 border border-amber-500/50 rounded-md text-amber-100 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"\\n                  flex-1 text-sm truncate font-handwritten transition-colors duration-200\\n                  \".concat(node.type === \"folder\" ? \"text-amber-200 font-medium\" : \"text-gray-300\", \"\\n                  \").concat(isSelected ? \"text-amber-100\" : \"\", \"\\n                  group-hover:text-amber-100\\n                \"),\n                                    onDoubleClick: startRename,\n                                    children: node.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, undefined),\n                                node.type === \"file\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MessageTokenViewer_MessageTokenStatusIndicator__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    fileId: node.id,\n                                    hasTokens: messageTokenStatus.get(node.id) === true,\n                                    isGenerating: isGeneratingTokens.has(node.id)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, undefined),\n            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: node.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeItem, {\n                        node: child,\n                        level: level + 1,\n                        isSelected: child.id === node.id,\n                        onSelect: onSelect,\n                        onToggle: onToggle,\n                        onContextMenu: onContextMenu,\n                        onRename: onRename,\n                        expandedNodes: expandedNodes,\n                        onFileMove: onFileMove,\n                        draggedNodeId: draggedNodeId,\n                        dragOverNodeId: dragOverNodeId,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        onDragOver: onDragOver,\n                        onDragLeave: onDragLeave,\n                        messageTokenStatus: messageTokenStatus,\n                        isGeneratingTokens: isGeneratingTokens\n                    }, child.id, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 578,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n        lineNumber: 475,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FileTreeItem, \"wRrtY2iV528S6uL9cnulZFxjHQg=\");\n_c3 = FileTreeItem;\nfunction FileTreePanel(param) {\n    let { artworkId, artworkTitle = \"作品\", currentFileId, onFileSelect, onFileCreate, onFileDelete, onFileRename, className = \"\" } = param;\n    _s1();\n    const [fileTree, setFileTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        \"root\"\n    ]));\n    const [selectedNodeId, setSelectedNodeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentFileId);\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        show: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    // 拖拽状态管理\n    const [draggedNodeId, setDraggedNodeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverNodeId, setDragOverNodeId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 作品信息状态\n    const [artworkInfo, setArtworkInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isArtworkExpanded, setIsArtworkExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // 作品根节点展开状态\n    ;\n    // 确认对话框状态\n    const [confirmDialog, setConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        title: \"\",\n        message: \"\",\n        onConfirm: ()=>{},\n        type: \"danger\"\n    });\n    // 消息标记状态管理\n    const [messageTokenStatus, setMessageTokenStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [isGeneratingTokens, setIsGeneratingTokens] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const fileTreeService = _services_fileTreeService__WEBPACK_IMPORTED_MODULE_2__.FileTreeService.getInstance();\n    const fileTreeEventService = _services_fileTreeEventService__WEBPACK_IMPORTED_MODULE_3__.FileTreeEventService.getInstance();\n    const fileAssociationService = _services_fileAssociationService__WEBPACK_IMPORTED_MODULE_4__.FileAssociationService.getInstance();\n    // 状态持久化相关函数\n    const getStateStorageKey = (key)=>\"fileTree_\".concat(artworkId, \"_\").concat(key);\n    // 保存文件树状态到localStorage\n    const saveFileTreeState = ()=>{\n        try {\n            // 保存选中的节点ID\n            if (selectedNodeId) {\n                localStorage.setItem(getStateStorageKey(\"selectedNodeId\"), selectedNodeId);\n            } else {\n                localStorage.removeItem(getStateStorageKey(\"selectedNodeId\"));\n            }\n            // 保存展开的节点集合\n            const expandedArray = Array.from(expandedNodes);\n            localStorage.setItem(getStateStorageKey(\"expandedNodes\"), JSON.stringify(expandedArray));\n            console.log(\"\\uD83D\\uDCBE 文件树状态已保存:\", {\n                selectedNodeId,\n                expandedNodes: expandedArray\n            });\n        } catch (error) {\n            console.warn(\"⚠️ 保存文件树状态失败:\", error);\n        }\n    };\n    // 从localStorage恢复文件树状态\n    const restoreFileTreeState = ()=>{\n        try {\n            // 恢复选中的节点ID\n            const savedSelectedNodeId = localStorage.getItem(getStateStorageKey(\"selectedNodeId\"));\n            if (savedSelectedNodeId) {\n                setSelectedNodeId(savedSelectedNodeId);\n                console.log(\"\\uD83D\\uDD04 已恢复选中节点:\", savedSelectedNodeId);\n            }\n            // 恢复展开的节点集合\n            const savedExpandedNodes = localStorage.getItem(getStateStorageKey(\"expandedNodes\"));\n            if (savedExpandedNodes) {\n                try {\n                    const expandedArray = JSON.parse(savedExpandedNodes);\n                    if (Array.isArray(expandedArray)) {\n                        const restoredExpanded = new Set(expandedArray);\n                        // 确保根节点始终展开\n                        restoredExpanded.add(\"root\");\n                        setExpandedNodes(restoredExpanded);\n                        console.log(\"\\uD83D\\uDD04 已恢复展开节点:\", expandedArray);\n                    }\n                } catch (parseError) {\n                    console.warn(\"⚠️ 解析展开节点数据失败:\", parseError);\n                    // 使用默认状态\n                    setExpandedNodes(new Set([\n                        \"root\"\n                    ]));\n                }\n            }\n        } catch (error) {\n            console.warn(\"⚠️ 恢复文件树状态失败:\", error);\n        }\n    };\n    // 加载文件树\n    const loadFileTree = async function() {\n        let silent = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            // 🔧 静默模式下不显示加载状态\n            if (!silent) {\n                setIsLoading(true);\n            }\n            setError(null);\n            const result = await fileTreeService.getFileTree(artworkId, expandedNodes);\n            if (result.success && result.data) {\n                setFileTree(result.data);\n                // 🔧 保持现有的展开状态，只在首次加载时设置默认展开\n                if (expandedNodes.size === 1 && expandedNodes.has(\"root\")) {\n                    // 首次加载：默认展开根节点和第一级文件夹\n                    const newExpanded = new Set([\n                        \"root\"\n                    ]);\n                    if (result.data.children) {\n                        result.data.children.forEach((child)=>{\n                            if (child.type === \"folder\") {\n                                newExpanded.add(child.id);\n                            }\n                        });\n                    }\n                    setExpandedNodes(newExpanded);\n                }\n            // 否则保持现有的展开状态\n            } else {\n                setError(result.error || \"加载文件树失败\");\n            }\n        } catch (err) {\n            setError(\"加载文件树时发生错误\");\n            console.error(\"加载文件树失败:\", err);\n        } finally{\n            // 🔧 静默模式下不修改加载状态\n            if (!silent) {\n                setIsLoading(false);\n            }\n        }\n    };\n    // 加载作品信息\n    const loadArtworkInfo = async ()=>{\n        try {\n            const { ArtworkService } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/services/artworkService */ \"(app-pages-browser)/./src/services/artworkService.ts\"));\n            const artworkService = ArtworkService.getInstance();\n            const result = await artworkService.getArtwork(artworkId);\n            if (result.success && result.data) {\n                setArtworkInfo({\n                    title: result.data.title\n                });\n            }\n        } catch (err) {\n            console.error(\"加载作品信息失败:\", err);\n            // 使用默认标题\n            setArtworkInfo({\n                title: artworkTitle\n            });\n        }\n    };\n    // 加载消息标记状态\n    const loadMessageTokenStatus = async ()=>{\n        if (!fileTree) return;\n        const statusMap = new Map();\n        const checkNodeStatus = async (node)=>{\n            if (node.type === \"file\") {\n                try {\n                    const hasTokens = await fileAssociationService.hasFileMessageTokens(node.id);\n                    statusMap.set(node.id, hasTokens);\n                } catch (error) {\n                    console.warn(\"检查消息标记状态失败:\", node.name, error);\n                    statusMap.set(node.id, false);\n                }\n            }\n            if (node.children) {\n                for (const child of node.children){\n                    await checkNodeStatus(child);\n                }\n            }\n        };\n        await checkNodeStatus(fileTree);\n        setMessageTokenStatus(statusMap);\n    };\n    // 生成消息标记\n    const generateMessageTokens = async (fileId, fileName)=>{\n        setIsGeneratingTokens((prev)=>new Set([\n                ...prev,\n                fileId\n            ]));\n        try {\n            // 使用默认的分割选项\n            const options = {\n                maxWords: 500,\n                minWords: 50,\n                language: \"auto\",\n                preserveParagraphs: true,\n                preserveCodeBlocks: true\n            };\n            await fileAssociationService.processFileForSegmentation(fileId, options, true);\n            // 更新状态\n            setMessageTokenStatus((prev)=>new Map(prev).set(fileId, true));\n            console.log(\"✅ 消息标记生成成功:\", fileName);\n        } catch (error) {\n            console.error(\"❌ 消息标记生成失败:\", fileName, error);\n            setError(\"生成消息标记失败: \".concat(error instanceof Error ? error.message : String(error)));\n        } finally{\n            setIsGeneratingTokens((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(fileId);\n                return newSet;\n            });\n        }\n    };\n    // 删除消息标记\n    const deleteMessageTokens = async (fileId, fileName)=>{\n        try {\n            await fileAssociationService.deleteFileMessageTokens(fileId);\n            // 更新状态\n            setMessageTokenStatus((prev)=>new Map(prev).set(fileId, false));\n            console.log(\"✅ 消息标记删除成功:\", fileName);\n        } catch (error) {\n            console.error(\"❌ 消息标记删除失败:\", fileName, error);\n            setError(\"删除消息标记失败: \".concat(error instanceof Error ? error.message : String(error)));\n        }\n    };\n    // 文件树刷新事件处理函数 - 使用静默模式\n    const handleFileTreeRefresh = ()=>{\n        console.log(\"\\uD83D\\uDD04 收到文件树刷新事件，开始静默重新加载文件树\");\n        loadFileTree(true) // 🔧 使用静默模式，不显示加载状态\n        ;\n    };\n    // 初始化加载\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (artworkId) {\n            // 先恢复保存的状态\n            restoreFileTreeState();\n            loadFileTree();\n            loadArtworkInfo();\n        }\n    }, [\n        artworkId\n    ]);\n    // 订阅文件树刷新事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 订阅刷新事件\n        const eventService = _services_fileTreeEventService__WEBPACK_IMPORTED_MODULE_3__.FileTreeEventService.getInstance();\n        eventService.subscribeRefresh(handleFileTreeRefresh);\n        console.log(\"✅ 文件树刷新事件监听已设置\");\n        return ()=>{\n            // 组件卸载时清理事件监听器\n            try {\n                eventService.unsubscribeRefresh(handleFileTreeRefresh);\n                console.log(\"\\uD83E\\uDDF9 文件树刷新事件监听已清理\");\n            } catch (error) {\n                console.warn(\"⚠️ 清理文件树刷新事件监听失败:\", error);\n            }\n        };\n    }, []) // 空依赖数组，只在组件挂载和卸载时执行\n    ;\n    // 加载消息标记状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (fileTree) {\n            loadMessageTokenStatus();\n        }\n    }, [\n        fileTree\n    ]);\n    // 监听currentFileId变化，同步选中状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentFileId && currentFileId !== selectedNodeId) {\n            setSelectedNodeId(currentFileId);\n            // 保存状态变化\n            setTimeout(()=>saveFileTreeState(), 0);\n        }\n    }, [\n        currentFileId\n    ]);\n    // 处理节点选择\n    const handleNodeSelect = (node)=>{\n        setSelectedNodeId(node.id);\n        if (onFileSelect && node.type === \"file\") {\n            onFileSelect(node);\n        }\n        // 保存状态变化\n        setTimeout(()=>saveFileTreeState(), 0);\n    };\n    // 处理节点展开/折叠\n    const handleNodeToggle = (nodeId)=>{\n        const newExpanded = new Set(expandedNodes);\n        if (newExpanded.has(nodeId)) {\n            newExpanded.delete(nodeId);\n        } else {\n            newExpanded.add(nodeId);\n        }\n        setExpandedNodes(newExpanded);\n        // 保存状态变化\n        setTimeout(()=>saveFileTreeState(), 0);\n    };\n    // 处理右键菜单\n    const handleContextMenu = (e, node)=>{\n        e.preventDefault();\n        setContextMenu({\n            show: true,\n            position: {\n                x: e.clientX,\n                y: e.clientY\n            },\n            node\n        });\n    };\n    // 关闭右键菜单\n    const closeContextMenu = ()=>{\n        setContextMenu({\n            show: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n    };\n    // 处理文件创建\n    const handleCreateFile = async (parentId, type)=>{\n        const name = type === \"file\" ? \"新文件.md\" : \"新文件夹\";\n        try {\n            if (type === \"file\") {\n                await fileTreeService.createFile(artworkId, parentId, name, \"markdown\");\n            } else {\n                await fileTreeService.createFolder(artworkId, parentId, name);\n            }\n            // 展开父节点（文件树会通过事件自动刷新）\n            setExpandedNodes((prev)=>new Set([\n                    ...prev,\n                    parentId\n                ]));\n            if (onFileCreate) {\n                onFileCreate(parentId, name, type);\n            }\n        } catch (err) {\n            console.error(\"创建文件失败:\", err);\n            setError(\"创建文件失败\");\n        }\n    };\n    // 图表文件创建功能已移除\n    // 处理XMind文件创建\n    const handleCreateXMindFile = async (parentId)=>{\n        const name = \"新XMind文件\";\n        try {\n            // 创建一个基础的XMind文件内容（Base64编码的空XMind文件）\n            const emptyXMindContent = \"UEsDBBQAAAAIAA==\" // 这是一个空的XMind文件的Base64表示\n            ;\n            const result = await fileTreeService.createFile(artworkId, parentId, \"\".concat(name, \".xmind\"), \"xmind\", emptyXMindContent);\n            if (result.success) {\n                // 展开父节点（文件树会通过事件自动刷新）\n                setExpandedNodes((prev)=>new Set([\n                        ...prev,\n                        parentId\n                    ]));\n                console.log(\"✅ XMind文件创建成功:\", name);\n                if (onFileCreate) {\n                    onFileCreate(parentId, \"\".concat(name, \".xmind\"), \"file\");\n                }\n            } else {\n                console.error(\"❌ 创建XMind文件失败:\", result.error);\n                setError(result.error || \"创建XMind文件失败\");\n            }\n        } catch (err) {\n            console.error(\"❌ 创建XMind文件异常:\", err);\n            setError(\"创建XMind文件失败\");\n        }\n    };\n    // 处理思维导图文件创建\n    const handleCreateMindMapFile = async (parentId)=>{\n        const name = \"新思维导图\";\n        try {\n            // 创建markdown格式的默认思维导图内容\n            const defaultMindMapContent = \"# \".concat(name, \"\\n\\n## 主要分支\\n\\n### 子节点\\n\\n在这里添加详细说明\\n\\n## 次要分支\\n\\n这里可以放更详细的内容\\n\\n## 其他想法\\n\\n- 添加更多分支\\n- 完善内容结构\\n- 丰富思维导图\");\n            const result = await fileTreeService.createFile(artworkId, parentId, \"\".concat(name, \".mindmap\"), \"mindmap\", defaultMindMapContent);\n            if (result.success) {\n                // 展开父节点（文件树会通过事件自动刷新）\n                setExpandedNodes((prev)=>new Set([\n                        ...prev,\n                        parentId\n                    ]));\n                console.log(\"✅ 思维导图文件创建成功:\", name);\n                if (onFileCreate) {\n                    onFileCreate(parentId, \"\".concat(name, \".mindmap.json\"), \"file\");\n                }\n            } else {\n                console.error(\"❌ 创建思维导图文件失败:\", result.error);\n                setError(result.error || \"创建思维导图文件失败\");\n            }\n        } catch (err) {\n            console.error(\"❌ 创建思维导图文件异常:\", err);\n            setError(\"创建思维导图文件失败\");\n        }\n    };\n    // 处理文件删除\n    const handleDeleteFile = async (fileId)=>{\n        setConfirmDialog({\n            isOpen: true,\n            title: \"删除文件\",\n            message: \"确定要删除这个文件吗？此操作不可撤销。\",\n            type: \"danger\",\n            onConfirm: async ()=>{\n                setConfirmDialog((prev)=>({\n                        ...prev,\n                        isOpen: false\n                    }));\n                try {\n                    await fileTreeService.deleteFile(fileId);\n                    // 文件树会通过事件自动刷新\n                    if (onFileDelete) {\n                        onFileDelete(fileId);\n                    }\n                } catch (err) {\n                    console.error(\"删除文件失败:\", err);\n                    setError(\"删除文件失败\");\n                }\n            }\n        });\n    };\n    // 处理文件重命名\n    const handleRename = async (fileId, newName)=>{\n        try {\n            await fileTreeService.renameFile(fileId, newName);\n            // 文件树会通过事件自动刷新\n            if (onFileRename) {\n                onFileRename(fileId, newName);\n            }\n        } catch (err) {\n            console.error(\"重命名文件失败:\", err);\n            setError(\"重命名文件失败\");\n        }\n    };\n    // 拖拽处理函数\n    const handleDragStart = (nodeId)=>{\n        setDraggedNodeId(nodeId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedNodeId(null);\n        setDragOverNodeId(null);\n    };\n    const handleDragOver = (nodeId)=>{\n        setDragOverNodeId(nodeId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverNodeId(null);\n    };\n    // 处理文件移动\n    const handleFileMove = async (fileId, targetParentId)=>{\n        try {\n            const result = await fileTreeService.moveFile(fileId, targetParentId);\n            if (result.success) {\n                // 文件树会通过事件自动刷新\n                console.log(\"✅ 文件移动成功\");\n                return true;\n            } else {\n                console.error(\"❌ 文件移动失败:\", result.error);\n                setError(result.error || \"文件移动失败\");\n                return false;\n            }\n        } catch (err) {\n            console.error(\"❌ 文件移动异常:\", err);\n            setError(\"文件移动时发生错误\");\n            return false;\n        }\n    };\n    // 右键菜单项\n    const getContextMenuItems = (node)=>{\n        const items = [];\n        if (node.type === \"folder\") {\n            items.push({\n                label: \"新建文件\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileIcon, {\n                    size: 14\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1124,\n                    columnNumber: 17\n                }, this),\n                onClick: ()=>{\n                    closeContextMenu();\n                    handleCreateFile(node.id, \"file\");\n                }\n            }, {\n                label: \"新建XMind\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"14\",\n                    height: \"14\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"12\",\n                            cy: \"8\",\n                            r: \"2\",\n                            fill: \"#9333EA\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1135,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"6\",\n                            cy: \"12\",\n                            r: \"1.5\",\n                            fill: \"#9333EA\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1136,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"18\",\n                            cy: \"12\",\n                            r: \"1.5\",\n                            fill: \"#9333EA\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1137,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"9\",\n                            cy: \"16\",\n                            r: \"1.5\",\n                            fill: \"#9333EA\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1138,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"15\",\n                            cy: \"16\",\n                            r: \"1.5\",\n                            fill: \"#9333EA\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1139,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"10.5\",\n                            y1: \"9\",\n                            x2: \"7.2\",\n                            y2: \"11.2\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1140,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"13.5\",\n                            y1: \"9\",\n                            x2: \"16.8\",\n                            y2: \"11.2\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1141,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"11\",\n                            y1: \"9.5\",\n                            x2: \"9.8\",\n                            y2: \"14.8\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1142,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"13\",\n                            y1: \"9.5\",\n                            x2: \"14.2\",\n                            y2: \"14.8\",\n                            stroke: \"#9333EA\",\n                            strokeWidth: \"1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1143,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1134,\n                    columnNumber: 13\n                }, this),\n                onClick: ()=>{\n                    closeContextMenu();\n                    handleCreateXMindFile(node.id);\n                }\n            }, {\n                label: \"新建思维导图\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    width: \"14\",\n                    height: \"14\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"2\",\n                            fill: \"#3B82F6\",\n                            stroke: \"#1E40AF\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1156,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"8\",\n                            cy: \"8\",\n                            r: \"1.2\",\n                            fill: \"#60A5FA\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1158,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"16\",\n                            cy: \"8\",\n                            r: \"1.2\",\n                            fill: \"#60A5FA\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1159,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"8\",\n                            cy: \"16\",\n                            r: \"1.2\",\n                            fill: \"#60A5FA\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1160,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"16\",\n                            cy: \"16\",\n                            r: \"1.2\",\n                            fill: \"#60A5FA\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1161,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"10.5\",\n                            y1: \"10.5\",\n                            x2: \"9.2\",\n                            y2: \"9.2\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1163,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"13.5\",\n                            y1: \"10.5\",\n                            x2: \"14.8\",\n                            y2: \"9.2\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1164,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"10.5\",\n                            y1: \"13.5\",\n                            x2: \"9.2\",\n                            y2: \"14.8\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1165,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                            x1: \"13.5\",\n                            y1: \"13.5\",\n                            x2: \"14.8\",\n                            y2: \"14.8\",\n                            stroke: \"#3B82F6\",\n                            strokeWidth: \"0.8\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1166,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1154,\n                    columnNumber: 13\n                }, this),\n                onClick: ()=>{\n                    closeContextMenu();\n                    handleCreateMindMapFile(node.id);\n                }\n            }, {\n                label: \"新建文件夹\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FolderIcon, {\n                    size: 14\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1176,\n                    columnNumber: 17\n                }, this),\n                onClick: ()=>{\n                    closeContextMenu();\n                    handleCreateFile(node.id, \"folder\");\n                }\n            });\n        }\n        // 文件的消息标记选项\n        if (node.type === \"file\") {\n            const hasTokens = messageTokenStatus.get(node.id) === true;\n            const isGenerating = isGeneratingTokens.has(node.id);\n            if (items.length > 0) {\n                items.push({\n                    type: \"divider\"\n                });\n            }\n            if (!hasTokens && !isGenerating) {\n                items.push({\n                    label: \"生成消息标记\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"14\",\n                        height: \"14\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1199,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1198,\n                        columnNumber: 13\n                    }, this),\n                    onClick: ()=>{\n                        closeContextMenu();\n                        generateMessageTokens(node.id, node.name);\n                    }\n                });\n            }\n            if (hasTokens) {\n                items.push({\n                    label: \"重新生成消息标记\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"14\",\n                        height: \"14\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1213,\n                        columnNumber: 13\n                    }, this),\n                    onClick: ()=>{\n                        closeContextMenu();\n                        generateMessageTokens(node.id, node.name);\n                    }\n                });\n                items.push({\n                    label: \"删除消息标记\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"14\",\n                        height: \"14\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1226,\n                        columnNumber: 13\n                    }, this),\n                    onClick: ()=>{\n                        closeContextMenu();\n                        deleteMessageTokens(node.id, node.name);\n                    },\n                    danger: true\n                });\n            }\n        }\n        if (node.name !== \"root\") {\n            if (items.length > 0) {\n                items.push({\n                    type: \"divider\"\n                });\n            }\n            items.push({\n                label: \"重命名\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SVGDecorations__WEBPACK_IMPORTED_MODULE_6__.SVGIcons.Create, {\n                    size: 14\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1247,\n                    columnNumber: 17\n                }, this),\n                onClick: ()=>{\n                    closeContextMenu();\n                    // 触发重命名模式\n                    const element = document.querySelector('[data-node-id=\"'.concat(node.id, '\"]'));\n                    if (element) {\n                        const nameSpan = element.querySelector(\"span\");\n                        if (nameSpan) {\n                            nameSpan.dispatchEvent(new Event(\"dblclick\", {\n                                bubbles: true\n                            }));\n                        }\n                    }\n                }\n            }, {\n                label: \"删除\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SVGDecorations__WEBPACK_IMPORTED_MODULE_6__.SVGIcons.Delete, {\n                    size: 14\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1262,\n                    columnNumber: 17\n                }, this),\n                onClick: ()=>{\n                    closeContextMenu();\n                    handleDeleteFile(node.id);\n                },\n                danger: true\n            });\n        }\n        return items;\n    };\n    // 加载状态\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center p-6 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 text-amber-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-5 h-5 border-2 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1280,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-handwritten\",\n                        children: \"加载文件树...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1281,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 1279,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 1278,\n            columnNumber: 7\n        }, this);\n    }\n    // 错误状态\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-400 text-sm text-center mb-3 font-handwritten\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1291,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadFileTree(),\n                    className: \"w-full px-3 py-2 text-sm text-amber-400 hover:text-amber-300 border border-amber-500/30 hover:border-amber-500/50 rounded-md transition-all duration-200 font-handwritten\",\n                    children: \"重试\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1294,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 1290,\n            columnNumber: 7\n        }, this);\n    }\n    // 空状态\n    if (!fileTree) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 text-center \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-400 text-sm font-handwritten mb-4\",\n                    children: \"暂无文件\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"右键点击文件夹创建新文件\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                    lineNumber: 1311,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n            lineNumber: 1307,\n            columnNumber: 7\n        }, this);\n    }\n    // 处理拖拽到作品根节点\n    const handleArtworkRootDrop = async (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragOverNodeId(null);\n        const draggedFileId = e.dataTransfer.getData(\"text/plain\");\n        if (draggedFileId && fileTree) {\n            // 移动到根节点（fileTree.id 是根节点ID）\n            const success = await handleFileMove(draggedFileId, fileTree.id);\n            if (success) {\n                console.log(\"✅ 文件移动到作品根级别成功\");\n            }\n        }\n    };\n    const handleArtworkRootDragOver = (e)=>{\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n        setDragOverNodeId(\"artwork-root\");\n    };\n    const handleArtworkRootDragLeave = ()=>{\n        setDragOverNodeId(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"file-tree-panel \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          flex items-center gap-2 px-3 py-2 mb-1 cursor-pointer\\n          bg-gradient-to-r from-amber-900/15 to-amber-800/5\\n          border-l-3 border-amber-600/50 rounded-r-lg mx-1 mt-1\\n          hover:from-amber-900/25 hover:to-amber-800/15\\n          hover:border-amber-500 transition-all duration-200\\n          \".concat(dragOverNodeId === \"artwork-root\" ? \"bg-amber-500/20 border-amber-500 border-2 border-dashed\" : \"\", \"\\n        \"),\n                onDrop: handleArtworkRootDrop,\n                onDragOver: handleArtworkRootDragOver,\n                onDragLeave: handleArtworkRootDragLeave,\n                onContextMenu: (e)=>{\n                    e.preventDefault();\n                    if (fileTree) {\n                        setContextMenu({\n                            show: true,\n                            position: {\n                                x: e.clientX,\n                                y: e.clientY\n                            },\n                            node: fileTree\n                        });\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"p-1 hover:bg-amber-500/20 rounded-md transition-all duration-200\",\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            setIsArtworkExpanded(!isArtworkExpanded);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"12\",\n                            height: \"12\",\n                            viewBox: \"0 0 12 12\",\n                            className: \"transition-transform duration-200 \".concat(isArtworkExpanded ? \"rotate-90\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M4 2l4 4-4 4\",\n                                fill: \"none\",\n                                stroke: \"#D4AF37\",\n                                strokeWidth: \"2\",\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                style: {\n                                    filter: \"drop-shadow(0 0 2px rgba(212, 175, 55, 0.4))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                                lineNumber: 1384,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1378,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArtworkIcon, {}, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                            lineNumber: 1400,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1399,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"flex-1 text-amber-100 font-medium text-sm font-handwritten truncate\",\n                        children: (artworkInfo === null || artworkInfo === void 0 ? void 0 : artworkInfo.title) || artworkTitle\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1404,\n                        columnNumber: 9\n                    }, this),\n                    dragOverNodeId === \"artwork-root\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-amber-300 opacity-75\",\n                        children: \"移动到根级别\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1410,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 1347,\n                columnNumber: 7\n            }, this),\n            isArtworkExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-auto h-full\",\n                children: fileTree.children && fileTree.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FileTreeItem, {\n                        node: child,\n                        level: 1,\n                        isSelected: selectedNodeId === child.id,\n                        onSelect: handleNodeSelect,\n                        onToggle: handleNodeToggle,\n                        onContextMenu: handleContextMenu,\n                        onRename: handleRename,\n                        expandedNodes: expandedNodes,\n                        onFileMove: handleFileMove,\n                        draggedNodeId: draggedNodeId,\n                        dragOverNodeId: dragOverNodeId,\n                        onDragStart: handleDragStart,\n                        onDragEnd: handleDragEnd,\n                        onDragOver: handleDragOver,\n                        onDragLeave: handleDragLeave,\n                        messageTokenStatus: messageTokenStatus,\n                        isGeneratingTokens: isGeneratingTokens\n                    }, child.id, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                        lineNumber: 1420,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 1418,\n                columnNumber: 9\n            }, this),\n            contextMenu.show && contextMenu.node && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                items: getContextMenuItems(contextMenu.node),\n                position: contextMenu.position,\n                onClose: closeContextMenu\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 1446,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PersonaManager_ConfirmDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmDialog.isOpen,\n                title: confirmDialog.title,\n                message: confirmDialog.message,\n                type: confirmDialog.type,\n                onConfirm: confirmDialog.onConfirm,\n                onCancel: ()=>setConfirmDialog((prev)=>({\n                            ...prev,\n                            isOpen: false\n                        }))\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n                lineNumber: 1454,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\FileTreePanel\\\\index.tsx\",\n        lineNumber: 1345,\n        columnNumber: 5\n    }, this);\n}\n_s1(FileTreePanel, \"hWxdNrsP5CJWBZfdkbEJyZLu9xk=\");\n_c4 = FileTreePanel;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"FolderIcon\");\n$RefreshReg$(_c1, \"FileIcon\");\n$RefreshReg$(_c2, \"ArtworkIcon\");\n$RefreshReg$(_c3, \"FileTreeItem\");\n$RefreshReg$(_c4, \"FileTreePanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FileTreePanel/index.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/fileTreeEventService.ts":
/*!**********************************************!*\
  !*** ./src/services/fileTreeEventService.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileTreeEventService: function() { return /* binding */ FileTreeEventService; }\n/* harmony export */ });\n/**\r\n * 文件树事件管理服务\r\n * 管理文件树刷新事件的订阅和通知机制\r\n * 基于PersonaService和ChatHistoryService的事件模式设计\r\n */ class FileTreeEventService {\n    /**\r\n   * 获取文件树事件服务单例\r\n   */ static getInstance() {\n        if (!FileTreeEventService.instance) {\n            FileTreeEventService.instance = new FileTreeEventService();\n        }\n        return FileTreeEventService.instance;\n    }\n    /**\r\n   * 订阅文件树刷新事件\r\n   */ subscribeRefresh(listener) {\n        this.refreshListeners.push(listener);\n        console.log(\"✅ 文件树刷新监听器已添加，当前监听器数量:\", this.refreshListeners.length);\n    }\n    /**\r\n   * 取消订阅文件树刷新事件\r\n   */ unsubscribeRefresh(listener) {\n        this.refreshListeners = this.refreshListeners.filter((l)=>l !== listener);\n        console.log(\"✅ 文件树刷新监听器已移除，当前监听器数量:\", this.refreshListeners.length);\n    }\n    /**\r\n   * 触发文件树刷新事件\r\n   */ triggerRefresh() {\n        console.log(\"\\uD83D\\uDD04 触发文件树刷新事件\");\n        this.notifyRefreshListeners();\n    }\n    /**\r\n   * 通知所有监听器文件树需要刷新 - 增强版本，带错误处理和状态验证\r\n   */ notifyRefreshListeners() {\n        const listenerCount = this.refreshListeners.length;\n        console.log(\"\\uD83D\\uDD14 通知文件树刷新:\", \"监听器数量:\", listenerCount);\n        if (listenerCount === 0) {\n            console.warn(\"⚠️ 没有文件树刷新监听器，通知将被忽略\");\n            return;\n        }\n        let successCount = 0;\n        let errorCount = 0;\n        for (const listener of this.refreshListeners){\n            try {\n                listener();\n                successCount++;\n            } catch (error) {\n                errorCount++;\n                console.error(\"❌ 文件树刷新监听器执行失败:\", error);\n                console.error(\"❌ 监听器函数:\", listener.toString().substring(0, 100) + \"...\");\n            }\n        }\n        if (errorCount > 0) {\n            console.warn(\"⚠️ 文件树刷新通知完成: 成功 \".concat(successCount, \"/\").concat(listenerCount, \", 失败 \").concat(errorCount));\n        } else {\n            console.log(\"✅ 文件树刷新通知成功: \".concat(successCount, \"/\").concat(listenerCount, \" 个监听器\"));\n        }\n    }\n    /**\r\n   * 获取当前刷新监听器数量\r\n   */ getRefreshListenerCount() {\n        return this.refreshListeners.length;\n    }\n    /**\r\n   * 订阅文件内容更新事件\r\n   */ subscribeFileContentUpdate(listener) {\n        this.fileContentUpdateListeners.push(listener);\n        console.log(\"✅ 文件内容更新监听器已添加，当前监听器数量:\", this.fileContentUpdateListeners.length);\n    }\n    /**\r\n   * 取消订阅文件内容更新事件\r\n   */ unsubscribeFileContentUpdate(listener) {\n        this.fileContentUpdateListeners = this.fileContentUpdateListeners.filter((l)=>l !== listener);\n        console.log(\"✅ 文件内容更新监听器已移除，当前监听器数量:\", this.fileContentUpdateListeners.length);\n    }\n    /**\r\n   * 触发文件内容更新事件\r\n   */ triggerFileContentUpdate(fileId) {\n        console.log(\"\\uD83D\\uDD04 触发文件内容更新事件:\", fileId);\n        this.notifyFileContentUpdateListeners(fileId);\n    }\n    /**\r\n   * 通知所有监听器文件内容已更新\r\n   */ notifyFileContentUpdateListeners(fileId) {\n        const listenerCount = this.fileContentUpdateListeners.length;\n        console.log(\"\\uD83D\\uDD14 通知文件内容更新:\", \"文件ID:\", fileId, \"监听器数量:\", listenerCount);\n        if (listenerCount === 0) {\n            console.warn(\"⚠️ 没有文件内容更新监听器，通知将被忽略\");\n            return;\n        }\n        let successCount = 0;\n        let errorCount = 0;\n        for (const listener of this.fileContentUpdateListeners){\n            try {\n                listener(fileId);\n                successCount++;\n            } catch (error) {\n                errorCount++;\n                console.error(\"❌ 文件内容更新监听器执行失败:\", error);\n                console.error(\"❌ 监听器函数:\", listener.toString().substring(0, 100) + \"...\");\n            }\n        }\n        if (errorCount > 0) {\n            console.warn(\"⚠️ 文件内容更新通知完成: 成功 \".concat(successCount, \"/\").concat(listenerCount, \", 失败 \").concat(errorCount));\n        } else {\n            console.log(\"✅ 文件内容更新通知成功: \".concat(successCount, \"/\").concat(listenerCount, \" 个监听器\"));\n        }\n    }\n    /**\r\n   * 清除所有监听器（用于测试或重置）\r\n   */ clearAllListeners() {\n        const refreshCount = this.refreshListeners.length;\n        const contentCount = this.fileContentUpdateListeners.length;\n        this.refreshListeners = [];\n        this.fileContentUpdateListeners = [];\n        console.log(\"\\uD83E\\uDDF9 已清除所有监听器: 文件树刷新 \".concat(refreshCount, \" 个, 文件内容更新 \").concat(contentCount, \" 个\"));\n    }\n    constructor(){\n        // 文件树刷新事件监听器\n        this.refreshListeners = [];\n        // 文件内容更新事件监听器\n        this.fileContentUpdateListeners = [];\n    // 私有构造函数，确保单例模式\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/fileTreeEventService.ts\n"));

/***/ })

});