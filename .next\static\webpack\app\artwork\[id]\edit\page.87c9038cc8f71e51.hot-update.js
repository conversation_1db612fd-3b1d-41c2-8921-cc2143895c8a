"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/MindMapRenderer/index.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./managers/CoreManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\");\n/* harmony import */ var _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./managers/EventManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/EventManager.ts\");\n/* harmony import */ var _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./managers/SelectionManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\");\n/* harmony import */ var _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./managers/StyleManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/StyleManager.ts\");\n/* harmony import */ var _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./managers/ClipboardManager */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/ClipboardManager.ts\");\n/* harmony import */ var _components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/MindMapContextMenu */ \"(app-pages-browser)/./src/components/MindMapRenderer/components/MindMapContextMenu.tsx\");\n/* harmony import */ var _styles_drag_enhancements_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./styles/drag-enhancements.css */ \"(app-pages-browser)/./src/components/MindMapRenderer/styles/drag-enhancements.css\");\n/**\r\n * MindMapRenderer - 思维导图渲染器\r\n * 基于SimpleMindMap官方API的区块化实现\r\n * \r\n * 设计理念：\r\n * - 严格遵循官方API最佳实践\r\n * - 区块化架构，职责分离\r\n * - 最小化封装，直接使用官方能力\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\r\n * MindMapRenderer 主组件\r\n * 负责组件生命周期管理和Manager协调\r\n */ const MindMapRenderer = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(_c = _s((props, ref)=>{\n    var _coreManagerRef_current;\n    _s();\n    const { data, parseResult, width = \"100%\", height = \"100%\", readonly = false, theme = \"dark\", loading = false, error, onRenderComplete, onNodeClick, onDataChange, onSelectionComplete, onDragStart, onDragEnd, className = \"\", config = {}, dragConfig = {}, enableDragEnhancement = true } = props;\n    // DOM引用\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Manager实例引用\n    const coreManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const selectionManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const styleManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dragEnhancerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const clipboardManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 组件状态\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasPerformedInitialFit, setHasPerformedInitialFit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 右键菜单状态\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        visible: false,\n        position: {\n            x: 0,\n            y: 0\n        },\n        node: null\n    });\n    /**\r\n   * 获取要渲染的数据\r\n   */ const getMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if ((parseResult === null || parseResult === void 0 ? void 0 : parseResult.success) && parseResult.data) {\n            return parseResult.data;\n        }\n        return data || null;\n    }, [\n        data,\n        parseResult\n    ]);\n    /**\r\n   * 处理右键菜单显示\r\n   */ const handleContextMenuShow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((position, node)=>{\n        setContextMenu({\n            visible: true,\n            position,\n            node\n        });\n        console.log(\"\\uD83C\\uDFAF 显示右键菜单:\", {\n            position,\n            node\n        });\n    }, []);\n    /**\r\n   * 处理右键菜单关闭\r\n   */ const handleContextMenuClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setContextMenu({\n            visible: false,\n            position: {\n                x: 0,\n                y: 0\n            },\n            node: null\n        });\n        console.log(\"\\uD83C\\uDFAF 关闭右键菜单\");\n    }, []);\n    /**\r\n   * 内部渲染完成处理\r\n   */ const handleRenderComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"✅ 思维导图渲染完成\");\n        // 只在首次渲染完成时适应画布\n        if (!hasPerformedInitialFit && coreManagerRef.current) {\n            setTimeout(()=>{\n                try {\n                    var _coreManagerRef_current_getInstance_view, _coreManagerRef_current_getInstance, _coreManagerRef_current;\n                    (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : (_coreManagerRef_current_getInstance = _coreManagerRef_current.getInstance()) === null || _coreManagerRef_current_getInstance === void 0 ? void 0 : (_coreManagerRef_current_getInstance_view = _coreManagerRef_current_getInstance.view) === null || _coreManagerRef_current_getInstance_view === void 0 ? void 0 : _coreManagerRef_current_getInstance_view.fit();\n                    setHasPerformedInitialFit(true);\n                    console.log(\"✅ 首次渲染完成，视图已适应画布\");\n                } catch (error) {\n                    console.error(\"❌ 首次适应画布失败:\", error);\n                    setHasPerformedInitialFit(true); // 即使失败也标记为已尝试\n                }\n            }, 100);\n        }\n        // 调用外部传入的回调\n        onRenderComplete === null || onRenderComplete === void 0 ? void 0 : onRenderComplete();\n    }, [\n        hasPerformedInitialFit,\n        onRenderComplete\n    ]);\n    /**\r\n   * 清理资源\r\n   */ const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // 🔧 防止重复清理，避免DOM操作冲突\n        if (!isInitialized) {\n            console.log(\"⚠️ 组件已清理，跳过重复清理\");\n            return;\n        }\n        console.log(\"\\uD83E\\uDDF9 开始清理思维导图组件资源\");\n        try {\n            var _dragEnhancerRef_current, _selectionManagerRef_current, _clipboardManagerRef_current, _eventManagerRef_current, _styleManagerRef_current, _coreManagerRef_current;\n            (_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.destroy();\n            dragEnhancerRef.current = null;\n            (_selectionManagerRef_current = selectionManagerRef.current) === null || _selectionManagerRef_current === void 0 ? void 0 : _selectionManagerRef_current.destroy();\n            selectionManagerRef.current = null;\n            (_clipboardManagerRef_current = clipboardManagerRef.current) === null || _clipboardManagerRef_current === void 0 ? void 0 : _clipboardManagerRef_current.destroy();\n            clipboardManagerRef.current = null;\n            (_eventManagerRef_current = eventManagerRef.current) === null || _eventManagerRef_current === void 0 ? void 0 : _eventManagerRef_current.destroy();\n            eventManagerRef.current = null;\n            (_styleManagerRef_current = styleManagerRef.current) === null || _styleManagerRef_current === void 0 ? void 0 : _styleManagerRef_current.destroy();\n            styleManagerRef.current = null;\n            (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.destroy();\n            coreManagerRef.current = null;\n            setIsInitialized(false);\n            console.log(\"✅ 思维导图组件资源清理完成\");\n        } catch (error) {\n            console.error(\"❌ 清理过程中出现错误:\", error);\n            // 确保状态被重置，即使清理过程中出错\n            setIsInitialized(false);\n        }\n    }, [\n        isInitialized\n    ]);\n    /**\r\n   * 初始化思维导图\r\n   */ const initializeMindMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        // 确保容器元素已挂载\n        if (!containerRef.current) {\n            if (retryCount < 5) {\n                console.warn(\"⚠️ Container element not ready, retrying... (\".concat(retryCount + 1, \"/5)\"));\n                // 逐步增加延迟时间\n                setTimeout(()=>{\n                    initializeMindMap(retryCount + 1);\n                }, 100 * (retryCount + 1)); // 100ms, 200ms, 300ms, 400ms, 500ms\n                return;\n            } else {\n                setRenderError(\"Container element not found after 5 retries\");\n                return;\n            }\n        }\n        const mindMapData = getMindMapData();\n        if (!mindMapData) {\n            setRenderError(\"No valid mind map data provided\");\n            return;\n        }\n        try {\n            // 清理现有实例\n            cleanup();\n            // 1. 初始化核心管理器\n            coreManagerRef.current = new _managers_CoreManager__WEBPACK_IMPORTED_MODULE_2__.CoreManager({\n                container: containerRef.current,\n                data: mindMapData,\n                readonly,\n                config\n            });\n            const mindMapInstance = await coreManagerRef.current.initialize();\n            // 2. 初始化样式管理器\n            styleManagerRef.current = new _managers_StyleManager__WEBPACK_IMPORTED_MODULE_5__.StyleManager(mindMapInstance, theme);\n            await styleManagerRef.current.initialize();\n            // 3. 初始化事件管理器\n            eventManagerRef.current = new _managers_EventManager__WEBPACK_IMPORTED_MODULE_3__.EventManager(mindMapInstance, {\n                onNodeClick,\n                onDataChange: onDataChange,\n                onRenderComplete,\n                onContextMenuShow: handleContextMenuShow,\n                onContextMenuHide: handleContextMenuClose\n            });\n            eventManagerRef.current.initialize();\n            // 4. 初始化剪贴板管理器\n            clipboardManagerRef.current = new _managers_ClipboardManager__WEBPACK_IMPORTED_MODULE_6__.ClipboardManager(mindMapInstance);\n            // 5. 初始化框选管理器（仅非只读模式）\n            if (!readonly) {\n                selectionManagerRef.current = new _managers_SelectionManager__WEBPACK_IMPORTED_MODULE_4__.SelectionManager(mindMapInstance, containerRef.current, onSelectionComplete);\n                selectionManagerRef.current.initialize();\n            }\n            // 5. 暂时禁用拖拽增强器，让SimpleMindMap原生拖拽功能正常工作\n            // TODO: 后续优化拖拽增强器与原生功能的兼容性\n            /*\r\n      if (!readonly && enableDragEnhancement) {\r\n        const enhancedDragConfig = {\r\n          ...dragConfig,\r\n          persistence: {\r\n            autoSave: dragConfig.persistence?.autoSave ?? true,\r\n            saveDelay: dragConfig.persistence?.saveDelay ?? 1000,\r\n            onSave: (data: any) => {\r\n              if (onDataChange) {\r\n                onDataChange(data);\r\n              }\r\n              if (dragConfig.persistence?.onSave) {\r\n                dragConfig.persistence.onSave(data);\r\n              }\r\n            }\r\n          }\r\n        };\r\n\r\n        dragEnhancerRef.current = new DragEnhancer(mindMapInstance, enhancedDragConfig);\r\n        dragEnhancerRef.current.initialize();\r\n      }\r\n      */ setIsInitialized(true);\n            setRenderError(null);\n        } catch (err) {\n            console.error(\"❌ 思维导图初始化失败:\", err);\n            setRenderError(err instanceof Error ? err.message : \"Failed to initialize mind map\");\n            setIsInitialized(false);\n        }\n    }, [\n        getMindMapData,\n        readonly,\n        theme,\n        config,\n        onNodeClick,\n        onDataChange,\n        onRenderComplete,\n        onSelectionComplete,\n        onDragStart,\n        onDragEnd,\n        enableDragEnhancement,\n        dragConfig,\n        cleanup\n    ]);\n    /**\r\n   * 更新思维导图数据\r\n   */ const updateMindMapData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) return;\n        const mindMapData = getMindMapData();\n        if (mindMapData) {\n            try {\n                coreManagerRef.current.updateData(mindMapData);\n                setRenderError(null);\n            } catch (err) {\n                console.error(\"Failed to update mind map data:\", err);\n                setRenderError(\"Failed to update mind map data\");\n            }\n        }\n    }, [\n        getMindMapData,\n        isInitialized\n    ]);\n    /**\r\n   * 手动触发画布尺寸更新\r\n   */ const resizeCanvas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!coreManagerRef.current || !isInitialized) {\n            console.warn(\"⚠️ 画布未初始化，无法调整尺寸\");\n            return;\n        }\n        try {\n            coreManagerRef.current.resize();\n        } catch (err) {\n            console.error(\"❌ 手动画布尺寸调整失败:\", err);\n        }\n    }, [\n        isInitialized\n    ]);\n    // 使用 useImperativeHandle 暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            resize: resizeCanvas,\n            fitView: ()=>{\n                if (coreManagerRef.current && isInitialized) {\n                    coreManagerRef.current.fitView();\n                }\n            },\n            getInstance: ()=>{\n                var _coreManagerRef_current;\n                return ((_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance()) || null;\n            },\n            isReady: ()=>isInitialized,\n            // 拖拽增强器相关方法\n            getDragState: ()=>{\n                var _dragEnhancerRef_current;\n                return ((_dragEnhancerRef_current = dragEnhancerRef.current) === null || _dragEnhancerRef_current === void 0 ? void 0 : _dragEnhancerRef_current.getDragState()) || null;\n            },\n            updateDragConfig: (newConfig)=>{\n                if (dragEnhancerRef.current) {\n                    dragEnhancerRef.current.updateConfig(newConfig);\n                }\n            },\n            // 🔧 清理方法，用于调试和故障排除\n            cleanup: ()=>{\n                console.log(\"\\uD83E\\uDDF9 强制清理思维导图组件资源\");\n                cleanup();\n            },\n            clearAllTimers: ()=>{\n                console.log(\"\\uD83D\\uDD12 强制清除所有定时器\");\n                cleanup();\n            }\n        }), [\n        resizeCanvas,\n        isInitialized,\n        cleanup\n    ]);\n    // 组件挂载时初始化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !error) {\n            // 延迟执行确保 DOM 已渲染\n            const timer = setTimeout(()=>{\n                initializeMindMap(0); // 从第0次重试开始\n            }, 50); // 减少初始延迟\n            return ()=>{\n                clearTimeout(timer);\n                cleanup();\n            };\n        }\n        return cleanup;\n    }, [\n        loading,\n        error\n    ]); // 移除函数依赖，避免不必要的重复执行\n    // 添加容器尺寸监听（优化版本，避免画布自动上移）\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!containerRef.current || !isInitialized) return;\n        const container = containerRef.current;\n        let resizeObserver = null;\n        let resizeTimeout = null;\n        let lastSize = {\n            width: 0,\n            height: 0\n        };\n        // 使用 ResizeObserver 监听容器尺寸变化\n        if (\"ResizeObserver\" in window) {\n            resizeObserver = new ResizeObserver((entries)=>{\n                for (const entry of entries){\n                    const { width, height } = entry.contentRect;\n                    // 只有当尺寸真正发生变化时才调整画布\n                    if (Math.abs(width - lastSize.width) > 1 || Math.abs(height - lastSize.height) > 1) {\n                        lastSize = {\n                            width,\n                            height\n                        };\n                        // 清除之前的定时器\n                        if (resizeTimeout) {\n                            clearTimeout(resizeTimeout);\n                        }\n                        // 防抖处理，避免频繁调用\n                        resizeTimeout = setTimeout(()=>{\n                            if (coreManagerRef.current) {\n                                coreManagerRef.current.resize();\n                            }\n                        }, 200); // 增加延迟时间，减少频繁调用\n                    }\n                }\n            });\n            resizeObserver.observe(container);\n        }\n        return ()=>{\n            if (resizeTimeout) {\n                clearTimeout(resizeTimeout);\n            }\n            if (resizeObserver) {\n                resizeObserver.disconnect();\n            }\n        };\n    }, [\n        isInitialized\n    ]);\n    // 数据变更时更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && !loading) {\n            updateMindMapData();\n        }\n    }, [\n        data,\n        parseResult,\n        isInitialized,\n        loading\n    ]); // 移除函数依赖\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container loading \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-200 font-handwritten\",\n                            children: \"正在加载思维导图...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 474,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 473,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 469,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染错误状态\n    if (error || renderError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mind-map-container error \".concat(className),\n            style: {\n                width,\n                height\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                            children: \"思维导图加载失败\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 text-sm mb-4\",\n                            children: error || renderError\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>initializeMindMap(0),\n                            className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 490,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n            lineNumber: 486,\n            columnNumber: 7\n        }, undefined);\n    }\n    // 渲染思维导图容器\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"mind-map-container \".concat(className),\n        style: {\n            width: \"100%\",\n            height: \"100%\",\n            background: \"#0a0a0a\",\n            border: \"2px solid #FFD700\",\n            borderRadius: \"8px\",\n            overflow: \"hidden\",\n            position: \"relative\",\n            boxSizing: \"border-box\",\n            boxShadow: \"0 4px 16px rgba(255, 215, 0, 0.3)\"\n        },\n        children: [\n            !readonly && isInitialized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"absolute\",\n                    top: \"10px\",\n                    right: \"10px\",\n                    background: \"rgba(255, 215, 0, 0.9)\",\n                    color: \"#000\",\n                    padding: \"6px 12px\",\n                    borderRadius: \"6px\",\n                    fontSize: \"12px\",\n                    zIndex: 100,\n                    pointerEvents: \"none\",\n                    fontFamily: \"var(--font-family-handwritten)\",\n                    fontWeight: \"600\",\n                    border: \"1px solid #FFA500\"\n                },\n                children: \"\\uD83D\\uDCA1 右键节点显示菜单，右键长按可框选\"\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 526,\n                columnNumber: 9\n            }, undefined),\n            !readonly && contextMenu.visible && contextMenu.node && clipboardManagerRef.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                node: contextMenu.node,\n                position: contextMenu.position,\n                mindMapInstance: (_coreManagerRef_current = coreManagerRef.current) === null || _coreManagerRef_current === void 0 ? void 0 : _coreManagerRef_current.getInstance(),\n                clipboardManager: clipboardManagerRef.current,\n                onClose: handleContextMenuClose\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n                lineNumber: 549,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\MindMapRenderer\\\\index.tsx\",\n        lineNumber: 509,\n        columnNumber: 5\n    }, undefined);\n}, \"n9iMgar1C7HJsnxniI5k3ycy3s4=\")), \"n9iMgar1C7HJsnxniI5k3ycy3s4=\");\n_c1 = MindMapRenderer;\nMindMapRenderer.displayName = \"MindMapRenderer\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (MindMapRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MindMapRenderer$React.forwardRef\");\n$RefreshReg$(_c1, \"MindMapRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\n"));

/***/ })

});