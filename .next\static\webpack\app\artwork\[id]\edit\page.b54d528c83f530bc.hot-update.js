"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts":
/*!*********************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/SelectionManager.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectionManager: function() { return /* binding */ SelectionManager; }\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * SelectionManager - 框选管理器\r\n * 负责右键长按框选功能的实现\r\n * \r\n * 职责：\r\n * - 右键长按检测\r\n * - 框选区域渲染\r\n * - 多节点选择逻辑\r\n * \r\n * 设计原则：\r\n * - 简化状态：只有 idle 和 selecting 两个状态\r\n * - 官方API优先：使用官方的坐标转换和节点选择API\r\n */ \nclass SelectionManager {\n    /**\r\n   * 初始化框选管理器\r\n   */ initialize() {\n        this.createSelectionBox();\n        this.bindEvents();\n        console.log(\"✅ 框选管理器初始化完成\");\n    }\n    /**\r\n   * 创建选择框DOM元素\r\n   */ createSelectionBox() {\n        this.selectionBox = document.createElement(\"div\");\n        this.selectionBox.className = \"mindmap-selection-box\";\n        const { selectionBoxStyle } = this.config;\n        this.selectionBox.style.cssText = \"\\n      position: fixed;\\n      pointer-events: none;\\n      z-index: 10000;\\n      border: \".concat(selectionBoxStyle.strokeWidth, \"px solid \").concat(selectionBoxStyle.strokeColor, \";\\n      background-color: \").concat(selectionBoxStyle.fillColor, \";\\n      opacity: \").concat(selectionBoxStyle.fillOpacity, \";\\n      border-radius: 4px;\\n      display: none;\\n      box-sizing: border-box;\\n      box-shadow: 0 2px 8px rgba(143, 188, 143, 0.3);\\n    \");\n        // 直接插入到body，避免容器层级问题\n        document.body.appendChild(this.selectionBox);\n    }\n    /**\r\n   * 绑定事件监听\r\n   */ bindEvents() {\n        // 不使用capture模式，让节点事件优先处理\n        this.container.addEventListener(\"mousedown\", this.boundHandlers.mousedown, {\n            passive: false\n        });\n        this.container.addEventListener(\"contextmenu\", this.boundHandlers.contextmenu, {\n            passive: false\n        });\n        // 全局鼠标事件\n        document.addEventListener(\"mousemove\", this.boundHandlers.mousemove, {\n            passive: false\n        });\n        document.addEventListener(\"mouseup\", this.boundHandlers.mouseup, {\n            passive: false\n        });\n    }\n    /**\r\n   * 解除事件监听\r\n   */ unbindEvents() {\n        this.container.removeEventListener(\"mousedown\", this.boundHandlers.mousedown, true);\n        this.container.removeEventListener(\"contextmenu\", this.boundHandlers.contextmenu, true);\n        document.removeEventListener(\"mousemove\", this.boundHandlers.mousemove);\n        document.removeEventListener(\"mouseup\", this.boundHandlers.mouseup);\n    }\n    /**\r\n   * 处理鼠标按下事件\r\n   */ handleMouseDown(event) {\n        // 只处理右键\n        if (event.button !== 2) return;\n        // 检查是否点击在节点上\n        const target = event.target;\n        if (this.isClickOnNode(target)) {\n            // 如果点击在节点上，不处理框选，让节点的右键菜单正常工作\n            console.log(\"\\uD83D\\uDDB1️ 右键点击节点，跳过框选处理\");\n            return;\n        }\n        // 阻止默认右键菜单\n        event.preventDefault();\n        // 转换为画布坐标\n        const canvasPoint = this.mindMapInstance.toPos(event.clientX, event.clientY);\n        this.startPoint = canvasPoint;\n        // 设置长按检测定时器\n        this.longPressTimer = setTimeout(()=>{\n            this.startSelection();\n        }, this.config.longPressDelay);\n        console.log(\"\\uD83D\\uDDB1️ 右键按下空白区域，开始检测长按\");\n    }\n    /**\r\n   * 处理鼠标移动事件\r\n   */ handleMouseMove(event) {\n        if (!this.startPoint) return;\n        const currentPoint = this.mindMapInstance.toPos(event.clientX, event.clientY);\n        this.currentPoint = currentPoint;\n        // 如果还在检测阶段，检查移动距离\n        if (!this.isSelecting && this.longPressTimer) {\n            const distance = this.calculateDistance(this.startPoint, currentPoint);\n            if (distance > this.config.moveThreshold) {\n                // 移动距离超过阈值，立即开始框选\n                this.clearLongPressTimer();\n                this.startSelection();\n            }\n        }\n        // 如果正在框选，更新选择框\n        if (this.isSelecting) {\n            this.updateSelectionBox();\n        }\n    }\n    /**\r\n   * 处理鼠标释放事件\r\n   */ handleMouseUp(event) {\n        if (event.button !== 2) return;\n        this.clearLongPressTimer();\n        if (this.isSelecting) {\n            this.completeSelection();\n        } else {\n            // 短按，重置状态\n            this.resetSelection();\n        }\n    }\n    /**\r\n   * 处理右键菜单事件\r\n   */ handleContextMenu(event) {\n        // 只有在正在框选时才阻止右键菜单\n        if (this.isSelecting) {\n            event.stopPropagation();\n            event.preventDefault();\n            console.log(\"\\uD83D\\uDEAB 框选状态中，阻止右键菜单\");\n        } else {\n            // 检查是否点击在节点上\n            const target = event.target;\n            if (!this.isClickOnNode(target)) {\n                // 如果不是点击节点，阻止默认右键菜单\n                event.preventDefault();\n                console.log(\"\\uD83D\\uDEAB 空白区域右键，阻止默认菜单\");\n            }\n        }\n    }\n    /**\r\n   * 开始框选\r\n   */ startSelection() {\n        this.isSelecting = true;\n        this.showSelectionBox();\n        console.log(\"\\uD83D\\uDCE6 开始框选模式\");\n    }\n    /**\r\n   * 显示选择框\r\n   */ showSelectionBox() {\n        if (this.selectionBox) {\n            this.selectionBox.style.display = \"block\";\n            this.updateSelectionBox();\n        }\n    }\n    /**\r\n   * 更新选择框位置和大小\r\n   */ updateSelectionBox() {\n        if (!this.selectionBox || !this.startPoint || !this.currentPoint) return;\n        // 转换为屏幕坐标\n        const containerRect = this.container.getBoundingClientRect();\n        const startScreen = {\n            x: this.startPoint.x + containerRect.left,\n            y: this.startPoint.y + containerRect.top\n        };\n        const currentScreen = {\n            x: this.currentPoint.x + containerRect.left,\n            y: this.currentPoint.y + containerRect.top\n        };\n        const left = Math.min(startScreen.x, currentScreen.x);\n        const top = Math.min(startScreen.y, currentScreen.y);\n        const width = Math.abs(currentScreen.x - startScreen.x);\n        const height = Math.abs(currentScreen.y - startScreen.y);\n        this.selectionBox.style.left = \"\".concat(left, \"px\");\n        this.selectionBox.style.top = \"\".concat(top, \"px\");\n        this.selectionBox.style.width = \"\".concat(width, \"px\");\n        this.selectionBox.style.height = \"\".concat(height, \"px\");\n    }\n    /**\r\n   * 完成框选\r\n   */ completeSelection() {\n        if (!this.startPoint || !this.currentPoint) {\n            this.resetSelection();\n            return;\n        }\n        // 查找选择范围内的节点\n        const selectedNodes = this.findNodesInSelection();\n        if (selectedNodes.length > 0) {\n            var // 回调通知\n            _this_onSelectionComplete, _this;\n            // 使用官方API激活节点\n            this.activateNodes(selectedNodes);\n            (_this_onSelectionComplete = (_this = this).onSelectionComplete) === null || _this_onSelectionComplete === void 0 ? void 0 : _this_onSelectionComplete.call(_this, selectedNodes);\n            console.log(\"✅ 框选完成，选中 \".concat(selectedNodes.length, \" 个节点\"));\n        } else {\n            console.log(\"ℹ️ 框选区域内没有节点\");\n        }\n        // 延迟隐藏选择框，提供视觉反馈\n        setTimeout(()=>{\n            this.resetSelection();\n        }, 150);\n    }\n    /**\r\n   * 查找选择范围内的节点\r\n   */ findNodesInSelection() {\n        var _this_mindMapInstance_renderer;\n        if (!this.startPoint || !this.currentPoint || !((_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.root)) {\n            return [];\n        }\n        const selectedNodes = [];\n        const selectionRect = this.getSelectionRect();\n        // 递归遍历节点树\n        this.traverseNodes(this.mindMapInstance.renderer.root, (node)=>{\n            if (this.isNodeInSelection(node, selectionRect)) {\n                selectedNodes.push(node);\n            }\n        });\n        return selectedNodes;\n    }\n    /**\r\n   * 递归遍历节点\r\n   */ traverseNodes(node, callback) {\n        if (!node) return;\n        callback(node);\n        if (node.children && Array.isArray(node.children)) {\n            node.children.forEach((child)=>this.traverseNodes(child, callback));\n        }\n    }\n    /**\r\n   * 检查节点是否在选择范围内\r\n   */ isNodeInSelection(node, selectionRect) {\n        try {\n            var _node_getRectInSvg;\n            // 使用官方API获取节点位置\n            const nodeRect = (_node_getRectInSvg = node.getRectInSvg) === null || _node_getRectInSvg === void 0 ? void 0 : _node_getRectInSvg.call(node);\n            if (!nodeRect) return false;\n            const { left, top, width, height } = nodeRect;\n            const nodeRight = left + width;\n            const nodeBottom = top + height;\n            // 矩形相交检测\n            return !(nodeRight < selectionRect.left || left > selectionRect.right || nodeBottom < selectionRect.top || top > selectionRect.bottom);\n        } catch (error) {\n            console.warn(\"❌ 节点位置检测失败:\", error);\n            return false;\n        }\n    }\n    /**\r\n   * 获取选择矩形\r\n   */ getSelectionRect() {\n        if (!this.startPoint || !this.currentPoint) {\n            return {\n                left: 0,\n                top: 0,\n                right: 0,\n                bottom: 0\n            };\n        }\n        const left = Math.min(this.startPoint.x, this.currentPoint.x);\n        const top = Math.min(this.startPoint.y, this.currentPoint.y);\n        const right = Math.max(this.startPoint.x, this.currentPoint.x);\n        const bottom = Math.max(this.startPoint.y, this.currentPoint.y);\n        return {\n            left,\n            top,\n            right,\n            bottom\n        };\n    }\n    /**\r\n   * 激活选中的节点\r\n   */ activateNodes(nodes) {\n        try {\n            var _this_mindMapInstance_renderer;\n            // 使用官方API激活多个节点\n            if ((_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.activeMultiNode) {\n                this.mindMapInstance.renderer.activeMultiNode(nodes);\n            } else {\n                var // 降级方案\n                _this_mindMapInstance_renderer1;\n                (_this_mindMapInstance_renderer1 = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer1 === void 0 ? void 0 : _this_mindMapInstance_renderer1.clearActiveNodeList();\n                nodes.forEach((node)=>{\n                    var _this_mindMapInstance_renderer;\n                    (_this_mindMapInstance_renderer = this.mindMapInstance.renderer) === null || _this_mindMapInstance_renderer === void 0 ? void 0 : _this_mindMapInstance_renderer.addNodeToActiveList(node);\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 激活节点失败:\", error);\n        }\n    }\n    /**\r\n   * 检查是否点击在节点上\r\n   */ isClickOnNode(target) {\n        // 检查点击目标是否是节点或节点的子元素\n        // SimpleMindMap的节点通常有特定的类名或属性\n        let element = target;\n        while(element && element !== this.container){\n            // 检查是否是SVG节点元素\n            if (element.tagName === \"g\" && element.getAttribute(\"data-nodeid\")) {\n                return true;\n            }\n            // 检查是否有节点相关的类名\n            if (element.classList && (element.classList.contains(\"smm-node\") || element.classList.contains(\"node\") || element.getAttribute(\"data-node\") !== null)) {\n                return true;\n            }\n            element = element.parentElement;\n        }\n        return false;\n    }\n    /**\r\n   * 计算两点间距离\r\n   */ calculateDistance(p1, p2) {\n        return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n    }\n    /**\r\n   * 清除长按定时器\r\n   */ clearLongPressTimer() {\n        if (this.longPressTimer) {\n            clearTimeout(this.longPressTimer);\n            this.longPressTimer = null;\n        }\n    }\n    /**\r\n   * 重置选择状态\r\n   */ resetSelection() {\n        this.isSelecting = false;\n        this.startPoint = null;\n        this.currentPoint = null;\n        this.clearLongPressTimer();\n        if (this.selectionBox) {\n            this.selectionBox.style.display = \"none\";\n        }\n    }\n    /**\r\n   * 更新配置\r\n   */ updateConfig(config) {\n        this.config = {\n            ...this.config,\n            ...config\n        };\n    }\n    /**\r\n   * 检查是否正在框选\r\n   */ isActive() {\n        return this.isSelecting;\n    }\n    /**\r\n   * 销毁框选管理器\r\n   */ destroy() {\n        this.resetSelection();\n        this.unbindEvents();\n        // 🔧 安全的DOM清理，避免与React冲突\n        if (this.selectionBox) {\n            try {\n                // 检查元素是否仍在DOM中\n                if (this.selectionBox.parentNode) {\n                    this.selectionBox.remove();\n                }\n            } catch (error) {\n                console.warn(\"⚠️ 框选元素清理失败:\", error);\n            }\n            this.selectionBox = null;\n        }\n        console.log(\"✅ 框选管理器销毁完成\");\n    }\n    constructor(mindMapInstance, container, onSelectionComplete){\n        // 简化状态：只有两种状态\n        this.isSelecting = false;\n        // 选择过程数据\n        this.startPoint = null;\n        this.currentPoint = null;\n        this.longPressTimer = null;\n        // DOM元素\n        this.selectionBox = null;\n        // 事件处理器绑定\n        this.boundHandlers = {\n            mousedown: this.handleMouseDown.bind(this),\n            mousemove: this.handleMouseMove.bind(this),\n            mouseup: this.handleMouseUp.bind(this),\n            contextmenu: this.handleContextMenu.bind(this)\n        };\n        this.mindMapInstance = mindMapInstance;\n        this.container = container;\n        this.config = _types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_SELECTION_CONFIG;\n        this.onSelectionComplete = onSelectionComplete;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/SelectionManager.ts\n"));

/***/ })

});