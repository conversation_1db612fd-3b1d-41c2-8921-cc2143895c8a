"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    const autoSaveTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const settingsDebounceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 🔧 文件切换时清除防抖定时器，防止旧文件内容保存到新文件\n        if (autoSaveTimeoutRef.current) {\n            clearTimeout(autoSaveTimeoutRef.current);\n            autoSaveTimeoutRef.current = undefined;\n            console.log(\"\\uD83D\\uDD12 文件切换时清除自动保存定时器\");\n        }\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        // 🔧 视图切换时清除防抖定时器，防止模式切换时的错误保存\n        if (autoSaveTimeoutRef.current) {\n            clearTimeout(autoSaveTimeoutRef.current);\n            autoSaveTimeoutRef.current = undefined;\n            console.log(\"\\uD83D\\uDD12 思维导图模式切换时清除自动保存定时器\");\n        }\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        // 防抖自动保存\n        if (settings.autoSave && onContentChange) {\n            if (autoSaveTimeoutRef.current) {\n                clearTimeout(autoSaveTimeoutRef.current);\n            }\n            autoSaveTimeoutRef.current = setTimeout(()=>{\n                onContentChange(content);\n            }, settings.autoSaveDelay);\n        }\n    };\n    // 处理思维导图数据变更\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 将思维导图数据转换为Markdown格式并保存\n        if (data && onContentChange) {\n            const markdownContent = convertMindMapToMarkdown(data);\n            // 防抖自动保存\n            if (autoSaveTimeoutRef.current) {\n                clearTimeout(autoSaveTimeoutRef.current);\n            }\n            autoSaveTimeoutRef.current = setTimeout(()=>{\n                onContentChange(markdownContent);\n                console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并自动保存\");\n            }, settings.autoSaveDelay);\n        }\n    }, [\n        onContentChange,\n        settings.autoSave,\n        settings.autoSaveDelay,\n        convertMindMapToMarkdown\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 防抖处理字符数变更\n    const handleWordWrapColumnChange = (value)=>{\n        // 清除之前的防抖定时器\n        if (settingsDebounceRef.current) {\n            clearTimeout(settingsDebounceRef.current);\n        }\n        // 设置新的防抖定时器\n        settingsDebounceRef.current = setTimeout(()=>{\n            handleSettingsChange({\n                wordWrapColumn: value\n            });\n        }, 300) // 300ms防抖延迟，平衡响应性和性能\n        ;\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (autoSaveTimeoutRef.current) {\n                clearTimeout(autoSaveTimeoutRef.current);\n            }\n            if (settingsDebounceRef.current) {\n                clearTimeout(settingsDebounceRef.current);\n            }\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, []);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 640,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 639,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 746,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 838,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 826,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 706,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 656,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 897,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 925,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 918,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 914,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 948,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 965,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 861,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 860,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 992,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 995,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 990,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 989,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1001,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1003,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1004,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 999,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1013,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 987,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1027,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1031,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1026,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1025,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1037,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1038,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1039,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1041,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1040,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1056,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1036,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1035,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1066,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1083,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1084,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1085,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1082,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1081,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1023,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1103,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1104,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1102,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1106,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1101,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1100,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1093,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1092,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 654,\n        columnNumber: 5\n    }, undefined);\n}, \"eMLkdTC7Lqb5OA/94M7ubwRGtZo=\")), \"eMLkdTC7Lqb5OA/94M7ubwRGtZo=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});