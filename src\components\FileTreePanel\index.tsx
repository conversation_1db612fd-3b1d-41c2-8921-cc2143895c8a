/**
 * 文件树面板组件
 * IDE风格的文件管理界面，支持拖拽和优化图标
 */

'use client'

import React, { useState, useEffect, useRef } from 'react'
import { FileTreeNode, EditorFileType } from '@/types'
import { FileTreeService } from '@/services/fileTreeService'
import { FileTreeEventService } from '@/services/fileTreeEventService'
import { FileAssociationService } from '@/services/fileAssociationService'
import { getFileTypeFromName } from '@/utils/fileTypeUtils'
import { SVGIcons } from '@/components/SVGDecorations'
import ContextMenu from '@/components/common/ContextMenu'
import ConfirmDialog from '@/components/PersonaManager/ConfirmDialog'
import MessageTokenStatusIndicator from '@/components/MessageTokenViewer/MessageTokenStatusIndicator'

// 优化的文件夹图标组件 - 更明显的金色设计
const FolderIcon: React.FC<{ isOpen?: boolean; size?: number }> = ({
  isOpen = false,
  size = 16
}) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    {isOpen ? (
      // 展开状态 - 打开的文件夹
      <g>
        <path
          d="M2 7V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V9C22 7.89543 21.1046 7 20 7H11L9 5H4C2.89543 5 2 5.89543 2 7Z"
          fill="#F0E68C"
          stroke="#D4AF37"
          strokeWidth="1.2"
          style={{
            filter: 'drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3))'
          }}
        />
        {/* 打开状态的内部区域 */}
        <path
          d="M4 9H20V18H4V9Z"
          fill="rgba(240, 230, 140, 0.4)"
          stroke="#B8860B"
          strokeWidth="0.5"
        />
        {/* 文件夹标签 */}
        <path
          d="M9 5H11L13 7H20"
          stroke="#D4AF37"
          strokeWidth="1"
          fill="none"
        />
        {/* 装饰性光泽 */}
        <ellipse cx="12" cy="13" rx="6" ry="2" fill="rgba(255, 255, 255, 0.2)" />
      </g>
    ) : (
      // 折叠状态 - 关闭的文件夹
      <g>
        <path
          d="M2 7V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V9C22 7.89543 21.1046 7 20 7H11L9 5H4C2.89543 5 2 5.89543 2 7Z"
          fill="#D4AF37"
          stroke="#B8860B"
          strokeWidth="1.2"
          style={{
            filter: 'drop-shadow(0 2px 4px rgba(212, 175, 55, 0.4))'
          }}
        />
        {/* 文件夹标签 */}
        <path
          d="M9 5H11L13 7H20"
          stroke="#F0E68C"
          strokeWidth="1"
          fill="none"
        />
        {/* 金色装饰点 */}
        <circle cx="8" cy="13" r="1" fill="#F0E68C" opacity="0.8" />
        <circle cx="16" cy="13" r="1" fill="#F0E68C" opacity="0.8" />
        {/* 装饰性光泽 */}
        <ellipse cx="12" cy="11" rx="5" ry="1.5" fill="rgba(255, 255, 255, 0.3)" />
      </g>
    )}
  </svg>
)

// 优化的文件图标组件 - 与文件夹明显区分
const FileIcon: React.FC<{ type?: EditorFileType; fileName?: string; size?: number }> = ({
  type,
  fileName = '',
  size = 16
}) => {
  // 使用fileTypeUtils进行统一文件类型检测
  const detectedType = type || getFileTypeFromName(fileName)
  const isChartFile = detectedType === 'chart'
  const isXMindFile = detectedType === 'xmind'
  const isMindMapFile = detectedType === 'mindmap'

  if (isMindMapFile) {
    return (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
          fill="rgba(16, 185, 129, 0.1)"
          stroke="#10B981"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{
            filter: 'drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3))'
          }}
        />
        {/* 文件折角 */}
        <path
          d="M14 2V8H20"
          stroke="#10B981"
          strokeWidth="1.2"
          fill="rgba(16, 185, 129, 0.1)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        {/* 思维导图图标 */}
        <g>
          {/* 中心节点 */}
          <circle cx="12" cy="12" r="2.5" fill="rgba(16, 185, 129, 0.3)" stroke="#10B981" strokeWidth="1" />
          {/* 分支节点 */}
          <circle cx="7" cy="9" r="1.5" fill="rgba(16, 185, 129, 0.2)" stroke="#10B981" strokeWidth="0.8" />
          <circle cx="17" cy="9" r="1.5" fill="rgba(16, 185, 129, 0.2)" stroke="#10B981" strokeWidth="0.8" />
          <circle cx="7" cy="15" r="1.5" fill="rgba(16, 185, 129, 0.2)" stroke="#10B981" strokeWidth="0.8" />
          <circle cx="17" cy="15" r="1.5" fill="rgba(16, 185, 129, 0.2)" stroke="#10B981" strokeWidth="0.8" />
          {/* 连接线 */}
          <line x1="10" y1="10.5" x2="8.5" y2="9.5" stroke="#10B981" strokeWidth="1" />
          <line x1="14" y1="10.5" x2="15.5" y2="9.5" stroke="#10B981" strokeWidth="1" />
          <line x1="10" y1="13.5" x2="8.5" y2="14.5" stroke="#10B981" strokeWidth="1" />
          <line x1="14" y1="13.5" x2="15.5" y2="14.5" stroke="#10B981" strokeWidth="1" />
          {/* 装饰性小节点 */}
          <circle cx="5" cy="7" r="0.8" fill="#10B981" opacity="0.6" />
          <circle cx="19" cy="7" r="0.8" fill="#10B981" opacity="0.6" />
          <circle cx="5" cy="17" r="0.8" fill="#10B981" opacity="0.6" />
          <circle cx="19" cy="17" r="0.8" fill="#10B981" opacity="0.6" />
          {/* 连接到小节点的线 */}
          <line x1="7" y1="8" x2="5.8" y2="7.2" stroke="#10B981" strokeWidth="0.6" opacity="0.6" />
          <line x1="17" y1="8" x2="18.2" y2="7.2" stroke="#10B981" strokeWidth="0.6" opacity="0.6" />
          <line x1="7" y1="16" x2="5.8" y2="16.8" stroke="#10B981" strokeWidth="0.6" opacity="0.6" />
          <line x1="17" y1="16" x2="18.2" y2="16.8" stroke="#10B981" strokeWidth="0.6" opacity="0.6" />
        </g>
      </svg>
    )
  }

  if (isXMindFile) {
    return (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
          fill="rgba(147, 51, 234, 0.1)"
          stroke="#9333EA"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{
            filter: 'drop-shadow(0 1px 2px rgba(147, 51, 234, 0.3))'
          }}
        />
        {/* 文件折角 */}
        <path
          d="M14 2V8H20"
          stroke="#9333EA"
          strokeWidth="1.2"
          fill="rgba(147, 51, 234, 0.1)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        {/* XMind文件图标 */}
        <g>
          {/* 中心节点 */}
          <circle cx="12" cy="12" r="2" fill="rgba(147, 51, 234, 0.3)" stroke="#9333EA" strokeWidth="0.8" />
          {/* 分支节点 */}
          <circle cx="8" cy="10" r="1.2" fill="rgba(147, 51, 234, 0.2)" stroke="#9333EA" strokeWidth="0.6" />
          <circle cx="16" cy="10" r="1.2" fill="rgba(147, 51, 234, 0.2)" stroke="#9333EA" strokeWidth="0.6" />
          <circle cx="8" cy="14" r="1.2" fill="rgba(147, 51, 234, 0.2)" stroke="#9333EA" strokeWidth="0.6" />
          <circle cx="16" cy="14" r="1.2" fill="rgba(147, 51, 234, 0.2)" stroke="#9333EA" strokeWidth="0.6" />
          {/* 连接线 */}
          <line x1="10.5" y1="11" x2="9.2" y2="10.2" stroke="#9333EA" strokeWidth="0.8" />
          <line x1="13.5" y1="11" x2="14.8" y2="10.2" stroke="#9333EA" strokeWidth="0.8" />
          <line x1="10.5" y1="13" x2="9.2" y2="13.8" stroke="#9333EA" strokeWidth="0.8" />
          <line x1="13.5" y1="13" x2="14.8" y2="13.8" stroke="#9333EA" strokeWidth="0.8" />
        </g>
      </svg>
    )
  }

  if (isChartFile) {
    return (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
          fill="rgba(59, 130, 246, 0.1)"
          stroke="#3B82F6"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{
            filter: 'drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3))'
          }}
        />
        {/* 文件折角 */}
        <path
          d="M14 2V8H20"
          stroke="#3B82F6"
          strokeWidth="1.2"
          fill="rgba(59, 130, 246, 0.1)"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        {/* 图表图标 */}
        <g>
          <rect x="7" y="10" width="10" height="8" rx="1" fill="rgba(59, 130, 246, 0.2)" stroke="#3B82F6" strokeWidth="0.5" />
          {/* 节点 */}
          <circle cx="9" cy="12" r="1" fill="#3B82F6" />
          <circle cx="15" cy="12" r="1" fill="#3B82F6" />
          <circle cx="12" cy="16" r="1" fill="#3B82F6" />
          {/* 连线 */}
          <line x1="9" y1="12" x2="15" y2="12" stroke="#3B82F6" strokeWidth="0.5" />
          <line x1="12" y1="12" x2="12" y2="16" stroke="#3B82F6" strokeWidth="0.5" />
        </g>
      </svg>
    )
  }

  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14 2H6C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8L14 2Z"
        fill="rgba(255, 255, 255, 0.05)"
        stroke="#9CA3AF"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{
          filter: 'drop-shadow(0 1px 2px rgba(156, 163, 175, 0.2))'
        }}
      />
      {/* 文件折角 */}
      <path
        d="M14 2V8H20"
        stroke="#9CA3AF"
        strokeWidth="1.2"
        fill="rgba(156, 163, 175, 0.1)"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      {/* 文件类型标识 */}
      {detectedType === 'markdown' && (
        <g>
          <rect x="8" y="14" width="8" height="4" rx="1" fill="rgba(245, 158, 11, 0.2)" stroke="#F59E0B" strokeWidth="0.5" />
          <text x="12" y="16.5" textAnchor="middle" fontSize="6" fill="#F59E0B" fontWeight="bold">
            MD
          </text>
        </g>
      )}
      {/* 文件内容线条 */}
      <g opacity="0.3">
        <line x1="7" y1="10" x2="13" y2="10" stroke="#9CA3AF" strokeWidth="0.5" />
        <line x1="7" y1="12" x2="15" y2="12" stroke="#9CA3AF" strokeWidth="0.5" />
        <line x1="7" y1="14" x2="11" y2="14" stroke="#9CA3AF" strokeWidth="0.5" />
      </g>
    </svg>
  )
}

// 作品根节点图标组件 - 特殊的作品图标
const ArtworkIcon: React.FC<{ size?: number }> = ({ size = 18 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g>
      {/* 作品本子/书籍样式 */}
      <path
        d="M4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V6C20 4.89543 19.1046 4 18 4H6C4.89543 4 4 4.89543 4 4Z"
        fill="rgba(139, 69, 19, 0.8)"
        stroke="#8B4513"
        strokeWidth="1.5"
        style={{
          filter: 'drop-shadow(0 3px 6px rgba(139, 69, 19, 0.4))'
        }}
      />
      {/* 书脊装饰 */}
      <path
        d="M4 4V20C4 21.1046 4.89543 22 6 22"
        stroke="#D2691E"
        strokeWidth="2"
        strokeLinecap="round"
      />
      {/* 封面装饰线条 */}
      <g opacity="0.7">
        <line x1="7" y1="8" x2="17" y2="8" stroke="#F4A460" strokeWidth="1" />
        <line x1="7" y1="11" x2="15" y2="11" stroke="#F4A460" strokeWidth="0.8" />
        <line x1="7" y1="14" x2="16" y2="14" stroke="#F4A460" strokeWidth="0.8" />
      </g>
      {/* 金色装饰点 */}
      <circle cx="8" cy="18" r="1" fill="#DAA520" opacity="0.8" />
      <circle cx="16" cy="18" r="1" fill="#DAA520" opacity="0.8" />
      {/* 光泽效果 */}
      <ellipse cx="12" cy="10" rx="6" ry="2" fill="rgba(255, 255, 255, 0.2)" />
    </g>
  </svg>
)

interface FileTreePanelProps {
  artworkId: string
  artworkTitle?: string // 作品标题，用于显示根节点
  currentFileId?: string | null
  onFileSelect?: (file: FileTreeNode) => void
  onFileCreate?: (parentId: string, name: string, type: 'file' | 'folder') => void
  onFileDelete?: (fileId: string) => void
  onFileRename?: (fileId: string, newName: string) => void
  className?: string
}

interface FileTreeItemProps {
  node: FileTreeNode
  level: number
  isSelected: boolean
  onSelect: (node: FileTreeNode) => void
  onToggle: (nodeId: string) => void
  onContextMenu: (e: React.MouseEvent, node: FileTreeNode) => void
  onRename: (nodeId: string, newName: string) => void
  expandedNodes: Set<string>
  onFileMove: (fileId: string, targetParentId: string) => Promise<boolean>
  draggedNodeId: string | null
  dragOverNodeId: string | null
  onDragStart: (nodeId: string) => void
  onDragEnd: () => void
  onDragOver: (nodeId: string) => void
  onDragLeave: () => void
  messageTokenStatus: Map<string, boolean>
  isGeneratingTokens: Set<string>
}

// 文件树项组件
const FileTreeItem: React.FC<FileTreeItemProps> = ({
  node,
  level,
  isSelected,
  onSelect,
  onToggle,
  onContextMenu,
  onRename,
  expandedNodes,
  onFileMove,
  draggedNodeId,
  dragOverNodeId,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDragLeave,
  messageTokenStatus,
  isGeneratingTokens
}) => {
  const [isRenaming, setIsRenaming] = useState(false)
  const [renamingValue, setRenamingValue] = useState(node.name)
  const inputRef = useRef<HTMLInputElement>(null)
  const expandTimeoutRef = useRef<NodeJS.Timeout>()

  const isExpanded = expandedNodes.has(node.id)
  const hasChildren = node.children && node.children.length > 0
  const isDragging = draggedNodeId === node.id
  const isDragOver = dragOverNodeId === node.id

  // 处理重命名
  const handleRename = () => {
    if (renamingValue.trim() && renamingValue !== node.name) {
      onRename(node.id, renamingValue.trim())
    }
    setIsRenaming(false)
    setRenamingValue(node.name)
  }

  // 开始重命名
  const startRename = () => {
    setIsRenaming(true)
    setRenamingValue(node.name)
    setTimeout(() => {
      inputRef.current?.focus()
      inputRef.current?.select()
    }, 0)
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename()
    } else if (e.key === 'Escape') {
      setIsRenaming(false)
      setRenamingValue(node.name)
    }
  }

  // 清理定时器
  useEffect(() => {
    return () => {
      if (expandTimeoutRef.current) {
        clearTimeout(expandTimeoutRef.current)
      }
    }
  }, [])

  // 拖拽事件处理
  const handleDragStart = (e: React.DragEvent) => {
    if (node.name === 'root') return // 根节点不能拖拽

    onDragStart(node.id)
    e.dataTransfer.setData('text/plain', node.id)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragEnd = () => {
    onDragEnd()
    if (expandTimeoutRef.current) {
      clearTimeout(expandTimeoutRef.current)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    if (node.type === 'folder' && !isDragging) {
      e.preventDefault()
      e.dataTransfer.dropEffect = 'move'
      onDragOver(node.id)

      // 悬停1秒后自动展开文件夹
      if (!isExpanded) {
        if (expandTimeoutRef.current) {
          clearTimeout(expandTimeoutRef.current)
        }
        expandTimeoutRef.current = setTimeout(() => {
          onToggle(node.id)
        }, 1000)
      }
    }
  }

  const handleDragLeave = (e: React.DragEvent) => {
    // 只有当鼠标真正离开元素时才触发
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX
    const y = e.clientY

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      onDragLeave()
      if (expandTimeoutRef.current) {
        clearTimeout(expandTimeoutRef.current)
      }
    }
  }

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onDragLeave()

    if (expandTimeoutRef.current) {
      clearTimeout(expandTimeoutRef.current)
    }

    if (node.type !== 'folder') return

    const draggedFileId = e.dataTransfer.getData('text/plain')
    if (draggedFileId && draggedFileId !== node.id) {
      const success = await onFileMove(draggedFileId, node.id)
      if (success) {
        // 确保目标文件夹展开以显示移动的文件
        if (!isExpanded) {
          onToggle(node.id)
        }
      }
    }
  }

  return (
    <div>
      {/* 文件/文件夹项 */}
      <div
        className={`
          flex items-center gap-2 px-3 py-2 cursor-pointer group
          hover:bg-amber-500/10 transition-all duration-200
          ${isSelected ? 'bg-gradient-to-r from-amber-500/20 to-amber-600/10 border-l-3 border-amber-500 shadow-lg shadow-amber-500/10' : ''}
          ${isDragging ? 'opacity-50 scale-95' : ''}
          ${isDragOver ? 'bg-amber-500/20 border-2 border-amber-500/50 border-dashed' : ''}
          rounded-r-lg mx-1 my-0.5
        `}
        style={{ paddingLeft: `${level * 20 + 12}px` }}
        draggable={node.name !== 'root'}
        onClick={() => onSelect(node)}
        onContextMenu={(e) => onContextMenu(e, node)}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* 展开/折叠按钮 */}
        {hasChildren && (
          <button
            className="p-1 hover:bg-amber-500/20 rounded-md transition-all duration-200 group-hover:bg-amber-500/15"
            onClick={(e) => {
              e.stopPropagation()
              onToggle(node.id)
            }}
          >
            <svg
              width="12"
              height="12"
              viewBox="0 0 12 12"
              className={`transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`}
            >
              <path
                d="M4 2l4 4-4 4"
                fill="none"
                stroke="#F59E0B"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                style={{
                  filter: 'drop-shadow(0 0 2px rgba(245, 158, 11, 0.4))'
                }}
              />
            </svg>
          </button>
        )}

        {/* 文件/文件夹图标 */}
        <div className="flex-shrink-0">
          {node.type === 'folder' ? (
            <FolderIcon isOpen={isExpanded} />
          ) : (
            <FileIcon
              fileName={node.name}
            />
          )}
        </div>

        {/* 文件名和状态指示器 */}
        <div className="flex-1 flex items-center gap-2">
          {isRenaming ? (
            <input
              ref={inputRef}
              type="text"
              value={renamingValue}
              onChange={(e) => setRenamingValue(e.target.value)}
              onBlur={handleRename}
              onKeyDown={handleKeyDown}
              className="flex-1 px-2 py-1 text-sm bg-gray-800 border border-amber-500/50 rounded-md text-amber-100 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
            />
          ) : (
            <>
              <span
                className={`
                  flex-1 text-sm truncate font-handwritten transition-colors duration-200
                  ${node.type === 'folder' ? 'text-amber-200 font-medium' : 'text-gray-300'}
                  ${isSelected ? 'text-amber-100' : ''}
                  group-hover:text-amber-100
                `}
                onDoubleClick={startRename}
              >
                {node.name}
              </span>

              {/* 消息标记状态指示器 */}
              {node.type === 'file' && (
                <MessageTokenStatusIndicator
                  fileId={node.id}
                  hasTokens={messageTokenStatus.get(node.id) === true}
                  isGenerating={isGeneratingTokens.has(node.id)}
                />
              )}
            </>
          )}
        </div>
      </div>

      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div>
          {node.children!.map((child) => (
            <FileTreeItem
              key={child.id}
              node={child}
              level={level + 1}
              isSelected={child.id === node.id}
              onSelect={onSelect}
              onToggle={onToggle}
              onContextMenu={onContextMenu}
              onRename={onRename}
              expandedNodes={expandedNodes}
              onFileMove={onFileMove}
              draggedNodeId={draggedNodeId}
              dragOverNodeId={dragOverNodeId}
              onDragStart={onDragStart}
              onDragEnd={onDragEnd}
              onDragOver={onDragOver}
              onDragLeave={onDragLeave}
              messageTokenStatus={messageTokenStatus}
              isGeneratingTokens={isGeneratingTokens}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default function FileTreePanel({
  artworkId,
  artworkTitle = '作品',
  currentFileId,
  onFileSelect,
  onFileCreate,
  onFileDelete,
  onFileRename,
  className = ''
}: FileTreePanelProps) {
  const [fileTree, setFileTree] = useState<FileTreeNode | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set(['root']))
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(currentFileId)
  const [contextMenu, setContextMenu] = useState<{
    show: boolean
    position: { x: number; y: number }
    node: FileTreeNode | null
  }>({ show: false, position: { x: 0, y: 0 }, node: null })

  // 拖拽状态管理
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null)
  const [dragOverNodeId, setDragOverNodeId] = useState<string | null>(null)

  // 作品信息状态
  const [artworkInfo, setArtworkInfo] = useState<{ title: string } | null>(null)
  const [isArtworkExpanded, setIsArtworkExpanded] = useState(true) // 作品根节点展开状态

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean
    title: string
    message: string
    onConfirm: () => void
    type?: 'danger' | 'warning' | 'info'
  }>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => { },
    type: 'danger'
  })

  // 消息标记状态管理
  const [messageTokenStatus, setMessageTokenStatus] = useState<Map<string, boolean>>(new Map())
  const [isGeneratingTokens, setIsGeneratingTokens] = useState<Set<string>>(new Set())

  const fileTreeService = FileTreeService.getInstance()
  const fileTreeEventService = FileTreeEventService.getInstance()
  const fileAssociationService = FileAssociationService.getInstance()

  // 状态持久化相关函数
  const getStateStorageKey = (key: string) => `fileTree_${artworkId}_${key}`

  // 保存文件树状态到localStorage
  const saveFileTreeState = () => {
    try {
      // 保存选中的节点ID
      if (selectedNodeId) {
        localStorage.setItem(getStateStorageKey('selectedNodeId'), selectedNodeId)
      } else {
        localStorage.removeItem(getStateStorageKey('selectedNodeId'))
      }

      // 保存展开的节点集合
      const expandedArray = Array.from(expandedNodes)
      localStorage.setItem(getStateStorageKey('expandedNodes'), JSON.stringify(expandedArray))

      console.log('💾 文件树状态已保存:', { selectedNodeId, expandedNodes: expandedArray })
    } catch (error) {
      console.warn('⚠️ 保存文件树状态失败:', error)
    }
  }

  // 从localStorage恢复文件树状态
  const restoreFileTreeState = () => {
    try {
      // 恢复选中的节点ID
      const savedSelectedNodeId = localStorage.getItem(getStateStorageKey('selectedNodeId'))
      if (savedSelectedNodeId) {
        setSelectedNodeId(savedSelectedNodeId)
        console.log('🔄 已恢复选中节点:', savedSelectedNodeId)
      }

      // 恢复展开的节点集合
      const savedExpandedNodes = localStorage.getItem(getStateStorageKey('expandedNodes'))
      if (savedExpandedNodes) {
        try {
          const expandedArray = JSON.parse(savedExpandedNodes)
          if (Array.isArray(expandedArray)) {
            const restoredExpanded = new Set(expandedArray)
            // 确保根节点始终展开
            restoredExpanded.add('root')
            setExpandedNodes(restoredExpanded)
            console.log('🔄 已恢复展开节点:', expandedArray)
          }
        } catch (parseError) {
          console.warn('⚠️ 解析展开节点数据失败:', parseError)
          // 使用默认状态
          setExpandedNodes(new Set(['root']))
        }
      }
    } catch (error) {
      console.warn('⚠️ 恢复文件树状态失败:', error)
    }
  }

  // 加载文件树
  const loadFileTree = async (silent: boolean = false) => {
    try {
      // 🔧 静默模式下不显示加载状态
      if (!silent) {
        setIsLoading(true)
      }
      setError(null)

      const result = await fileTreeService.getFileTree(artworkId, expandedNodes)

      if (result.success && result.data) {
        setFileTree(result.data)
        // 🔧 保持现有的展开状态，只在首次加载时设置默认展开
        if (expandedNodes.size === 1 && expandedNodes.has('root')) {
          // 首次加载：默认展开根节点和第一级文件夹
          const newExpanded = new Set(['root'])
          if (result.data.children) {
            result.data.children.forEach(child => {
              if (child.type === 'folder') {
                newExpanded.add(child.id)
              }
            })
          }
          setExpandedNodes(newExpanded)
        }
        // 否则保持现有的展开状态
      } else {
        setError(result.error || '加载文件树失败')
      }
    } catch (err) {
      setError('加载文件树时发生错误')
      console.error('加载文件树失败:', err)
    } finally {
      // 🔧 静默模式下不修改加载状态
      if (!silent) {
        setIsLoading(false)
      }
    }
  }

  // 加载作品信息
  const loadArtworkInfo = async () => {
    try {
      const { ArtworkService } = await import('@/services/artworkService')
      const artworkService = ArtworkService.getInstance()
      const result = await artworkService.getArtwork(artworkId)

      if (result.success && result.data) {
        setArtworkInfo({ title: result.data.title })
      }
    } catch (err) {
      console.error('加载作品信息失败:', err)
      // 使用默认标题
      setArtworkInfo({ title: artworkTitle })
    }
  }

  // 加载消息标记状态
  const loadMessageTokenStatus = async () => {
    if (!fileTree) return

    const statusMap = new Map<string, boolean>()

    const checkNodeStatus = async (node: FileTreeNode) => {
      if (node.type === 'file') {
        try {
          const hasTokens = await fileAssociationService.hasFileMessageTokens(node.id)
          statusMap.set(node.id, hasTokens)
        } catch (error) {
          console.warn('检查消息标记状态失败:', node.name, error)
          statusMap.set(node.id, false)
        }
      }

      if (node.children) {
        for (const child of node.children) {
          await checkNodeStatus(child)
        }
      }
    }

    await checkNodeStatus(fileTree)
    setMessageTokenStatus(statusMap)
  }

  // 生成消息标记
  const generateMessageTokens = async (fileId: string, fileName: string) => {
    setIsGeneratingTokens(prev => new Set([...prev, fileId]))

    try {
      // 使用默认的分割选项
      const options = {
        maxWords: 500,
        minWords: 50,
        language: 'auto' as const,
        preserveParagraphs: true,
        preserveCodeBlocks: true
      }

      await fileAssociationService.processFileForSegmentation(fileId, options, true)

      // 更新状态
      setMessageTokenStatus(prev => new Map(prev).set(fileId, true))

      console.log('✅ 消息标记生成成功:', fileName)

    } catch (error) {
      console.error('❌ 消息标记生成失败:', fileName, error)
      setError(`生成消息标记失败: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setIsGeneratingTokens(prev => {
        const newSet = new Set(prev)
        newSet.delete(fileId)
        return newSet
      })
    }
  }

  // 删除消息标记
  const deleteMessageTokens = async (fileId: string, fileName: string) => {
    try {
      await fileAssociationService.deleteFileMessageTokens(fileId)

      // 更新状态
      setMessageTokenStatus(prev => new Map(prev).set(fileId, false))

      console.log('✅ 消息标记删除成功:', fileName)

    } catch (error) {
      console.error('❌ 消息标记删除失败:', fileName, error)
      setError(`删除消息标记失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  // 文件树刷新事件处理函数 - 使用静默模式
  const handleFileTreeRefresh = () => {
    console.log('🔄 收到文件树刷新事件，开始静默重新加载文件树')
    loadFileTree(true) // 🔧 使用静默模式，不显示加载状态
  }

  // 初始化加载
  useEffect(() => {
    if (artworkId) {
      // 先恢复保存的状态
      restoreFileTreeState()
      loadFileTree()
      loadArtworkInfo()
    }
  }, [artworkId])

  // 订阅文件树刷新事件
  useEffect(() => {
    // 订阅刷新事件
    const eventService = FileTreeEventService.getInstance()
    eventService.subscribeRefresh(handleFileTreeRefresh)
    console.log('✅ 文件树刷新事件监听已设置')

    return () => {
      // 组件卸载时清理事件监听器
      try {
        eventService.unsubscribeRefresh(handleFileTreeRefresh)
        console.log('🧹 文件树刷新事件监听已清理')
      } catch (error) {
        console.warn('⚠️ 清理文件树刷新事件监听失败:', error)
      }
    }
  }, []) // 空依赖数组，只在组件挂载和卸载时执行

  // 加载消息标记状态
  useEffect(() => {
    if (fileTree) {
      loadMessageTokenStatus()
    }
  }, [fileTree])

  // 监听currentFileId变化，同步选中状态
  useEffect(() => {
    if (currentFileId && currentFileId !== selectedNodeId) {
      setSelectedNodeId(currentFileId)
      // 保存状态变化
      setTimeout(() => saveFileTreeState(), 0)
    }
  }, [currentFileId])

  // 处理节点选择
  const handleNodeSelect = (node: FileTreeNode) => {
    setSelectedNodeId(node.id)
    if (onFileSelect && node.type === 'file') {
      onFileSelect(node)
    }
    // 保存状态变化
    setTimeout(() => saveFileTreeState(), 0)
  }

  // 处理节点展开/折叠
  const handleNodeToggle = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes)
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpandedNodes(newExpanded)
    // 保存状态变化
    setTimeout(() => saveFileTreeState(), 0)
  }

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, node: FileTreeNode) => {
    e.preventDefault()
    setContextMenu({
      show: true,
      position: { x: e.clientX, y: e.clientY },
      node
    })
  }

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu({ show: false, position: { x: 0, y: 0 }, node: null })
  }

  // 处理文件创建
  const handleCreateFile = async (parentId: string, type: 'file' | 'folder') => {
    const name = type === 'file' ? '新文件.md' : '新文件夹'

    try {
      if (type === 'file') {
        await fileTreeService.createFile(artworkId, parentId, name, 'markdown')
      } else {
        await fileTreeService.createFolder(artworkId, parentId, name)
      }

      // 展开父节点（文件树会通过事件自动刷新）
      setExpandedNodes(prev => new Set([...prev, parentId]))

      if (onFileCreate) {
        onFileCreate(parentId, name, type)
      }
    } catch (err) {
      console.error('创建文件失败:', err)
      setError('创建文件失败')
    }
  }

  // 图表文件创建功能已移除

  // 处理XMind文件创建
  const handleCreateXMindFile = async (parentId: string) => {
    const name = '新XMind文件'

    try {
      // 创建一个基础的XMind文件内容（Base64编码的空XMind文件）
      const emptyXMindContent = 'UEsDBBQAAAAIAA==' // 这是一个空的XMind文件的Base64表示

      const result = await fileTreeService.createFile(artworkId, parentId, `${name}.xmind`, 'xmind', emptyXMindContent)

      if (result.success) {
        // 展开父节点（文件树会通过事件自动刷新）
        setExpandedNodes(prev => new Set([...prev, parentId]))

        console.log('✅ XMind文件创建成功:', name)

        if (onFileCreate) {
          onFileCreate(parentId, `${name}.xmind`, 'file')
        }
      } else {
        console.error('❌ 创建XMind文件失败:', result.error)
        setError(result.error || '创建XMind文件失败')
      }
    } catch (err) {
      console.error('❌ 创建XMind文件异常:', err)
      setError('创建XMind文件失败')
    }
  }

  // 处理思维导图文件创建
  const handleCreateMindMapFile = async (parentId: string) => {
    const name = '新思维导图'

    try {
      // 创建markdown格式的默认思维导图内容
      const defaultMindMapContent = `# ${name}

## 主要分支

### 子节点

在这里添加详细说明

## 次要分支

这里可以放更详细的内容

## 其他想法

- 添加更多分支
- 完善内容结构
- 丰富思维导图`

      const result = await fileTreeService.createFile(artworkId, parentId, `${name}.mindmap`, 'mindmap', defaultMindMapContent)

      if (result.success) {
        // 展开父节点（文件树会通过事件自动刷新）
        setExpandedNodes(prev => new Set([...prev, parentId]))

        console.log('✅ 思维导图文件创建成功:', name)

        if (onFileCreate) {
          onFileCreate(parentId, `${name}.mindmap.json`, 'file')
        }
      } else {
        console.error('❌ 创建思维导图文件失败:', result.error)
        setError(result.error || '创建思维导图文件失败')
      }
    } catch (err) {
      console.error('❌ 创建思维导图文件异常:', err)
      setError('创建思维导图文件失败')
    }
  }

  // 处理文件删除
  const handleDeleteFile = async (fileId: string) => {
    setConfirmDialog({
      isOpen: true,
      title: '删除文件',
      message: '确定要删除这个文件吗？此操作不可撤销。',
      type: 'danger',
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }))

        try {
          await fileTreeService.deleteFile(fileId)
          // 文件树会通过事件自动刷新

          if (onFileDelete) {
            onFileDelete(fileId)
          }
        } catch (err) {
          console.error('删除文件失败:', err)
          setError('删除文件失败')
        }
      }
    })
  }

  // 处理文件重命名
  const handleRename = async (fileId: string, newName: string) => {
    try {
      await fileTreeService.renameFile(fileId, newName)
      // 文件树会通过事件自动刷新

      if (onFileRename) {
        onFileRename(fileId, newName)
      }
    } catch (err) {
      console.error('重命名文件失败:', err)
      setError('重命名文件失败')
    }
  }

  // 拖拽处理函数
  const handleDragStart = (nodeId: string) => {
    setDraggedNodeId(nodeId)
  }

  const handleDragEnd = () => {
    setDraggedNodeId(null)
    setDragOverNodeId(null)
  }

  const handleDragOver = (nodeId: string) => {
    setDragOverNodeId(nodeId)
  }

  const handleDragLeave = () => {
    setDragOverNodeId(null)
  }

  // 处理文件移动
  const handleFileMove = async (fileId: string, targetParentId: string): Promise<boolean> => {
    try {
      const result = await fileTreeService.moveFile(fileId, targetParentId)

      if (result.success) {
        // 文件树会通过事件自动刷新
        console.log('✅ 文件移动成功')
        return true
      } else {
        console.error('❌ 文件移动失败:', result.error)
        setError(result.error || '文件移动失败')
        return false
      }
    } catch (err) {
      console.error('❌ 文件移动异常:', err)
      setError('文件移动时发生错误')
      return false
    }
  }

  // 右键菜单项
  const getContextMenuItems = (node: FileTreeNode) => {
    const items = []

    if (node.type === 'folder') {
      items.push(
        {
          label: '新建文件',
          icon: <FileIcon size={14} />,
          onClick: () => {
            closeContextMenu()
            handleCreateFile(node.id, 'file')
          }
        },

        {
          label: '新建XMind',
          icon: (
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <circle cx="12" cy="8" r="2" fill="#9333EA" />
              <circle cx="6" cy="12" r="1.5" fill="#9333EA" />
              <circle cx="18" cy="12" r="1.5" fill="#9333EA" />
              <circle cx="9" cy="16" r="1.5" fill="#9333EA" />
              <circle cx="15" cy="16" r="1.5" fill="#9333EA" />
              <line x1="10.5" y1="9" x2="7.2" y2="11.2" stroke="#9333EA" strokeWidth="1" />
              <line x1="13.5" y1="9" x2="16.8" y2="11.2" stroke="#9333EA" strokeWidth="1" />
              <line x1="11" y1="9.5" x2="9.8" y2="14.8" stroke="#9333EA" strokeWidth="1" />
              <line x1="13" y1="9.5" x2="14.2" y2="14.8" stroke="#9333EA" strokeWidth="1" />
            </svg>
          ),
          onClick: () => {
            closeContextMenu()
            handleCreateXMindFile(node.id)
          }
        },
        {
          label: '新建思维导图',
          icon: (
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              {/* 中心节点 */}
              <circle cx="12" cy="12" r="2" fill="#3B82F6" stroke="#1E40AF" strokeWidth="0.8" />
              {/* 分支节点 */}
              <circle cx="8" cy="8" r="1.2" fill="#60A5FA" stroke="#3B82F6" strokeWidth="0.6" />
              <circle cx="16" cy="8" r="1.2" fill="#60A5FA" stroke="#3B82F6" strokeWidth="0.6" />
              <circle cx="8" cy="16" r="1.2" fill="#60A5FA" stroke="#3B82F6" strokeWidth="0.6" />
              <circle cx="16" cy="16" r="1.2" fill="#60A5FA" stroke="#3B82F6" strokeWidth="0.6" />
              {/* 连接线 */}
              <line x1="10.5" y1="10.5" x2="9.2" y2="9.2" stroke="#3B82F6" strokeWidth="0.8" />
              <line x1="13.5" y1="10.5" x2="14.8" y2="9.2" stroke="#3B82F6" strokeWidth="0.8" />
              <line x1="10.5" y1="13.5" x2="9.2" y2="14.8" stroke="#3B82F6" strokeWidth="0.8" />
              <line x1="13.5" y1="13.5" x2="14.8" y2="14.8" stroke="#3B82F6" strokeWidth="0.8" />
            </svg>
          ),
          onClick: () => {
            closeContextMenu()
            handleCreateMindMapFile(node.id)
          }
        },
        {
          label: '新建文件夹',
          icon: <FolderIcon size={14} />,
          onClick: () => {
            closeContextMenu()
            handleCreateFile(node.id, 'folder')
          }
        }
      )
    }

    // 文件的消息标记选项
    if (node.type === 'file') {
      const hasTokens = messageTokenStatus.get(node.id) === true
      const isGenerating = isGeneratingTokens.has(node.id)

      if (items.length > 0) {
        items.push({ type: 'divider' as const })
      }

      if (!hasTokens && !isGenerating) {
        items.push({
          label: '生成消息标记',
          icon: (
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z" />
            </svg>
          ),
          onClick: () => {
            closeContextMenu()
            generateMessageTokens(node.id, node.name)
          }
        })
      }

      if (hasTokens) {
        items.push({
          label: '重新生成消息标记',
          icon: (
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
            </svg>
          ),
          onClick: () => {
            closeContextMenu()
            generateMessageTokens(node.id, node.name)
          }
        })

        items.push({
          label: '删除消息标记',
          icon: (
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
            </svg>
          ),
          onClick: () => {
            closeContextMenu()
            deleteMessageTokens(node.id, node.name)
          },
          danger: true
        })
      }
    }

    if (node.name !== 'root') {
      if (items.length > 0) {
        items.push({ type: 'divider' as const })
      }

      items.push(
        {
          label: '重命名',
          icon: <SVGIcons.Create size={14} />,
          onClick: () => {
            closeContextMenu()
            // 触发重命名模式
            const element = document.querySelector(`[data-node-id="${node.id}"]`)
            if (element) {
              const nameSpan = element.querySelector('span')
              if (nameSpan) {
                nameSpan.dispatchEvent(new Event('dblclick', { bubbles: true }))
              }
            }
          }
        },
        {
          label: '删除',
          icon: <SVGIcons.Delete size={14} />,
          onClick: () => {
            closeContextMenu()
            handleDeleteFile(node.id)
          },
          danger: true
        }
      )
    }

    return items
  }

  // 加载状态
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-6 ${className}`}>
        <div className="flex items-center gap-3 text-amber-400">
          <div className="w-5 h-5 border-2 border-amber-400 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-sm font-handwritten">加载文件树...</span>
        </div>
      </div>
    )
  }

  // 错误状态
  if (error) {
    return (
      <div className={`p-4 ${className}`}>
        <div className="text-red-400 text-sm text-center mb-3 font-handwritten">
          {error}
        </div>
        <button
          onClick={() => loadFileTree()}
          className="w-full px-3 py-2 text-sm text-amber-400 hover:text-amber-300 border border-amber-500/30 hover:border-amber-500/50 rounded-md transition-all duration-200 font-handwritten"
        >
          重试
        </button>
      </div>
    )
  }

  // 空状态
  if (!fileTree) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <div className="text-gray-400 text-sm font-handwritten mb-4">
          暂无文件
        </div>
        <div className="text-xs text-gray-500">
          右键点击文件夹创建新文件
        </div>
      </div>
    )
  }

  // 处理拖拽到作品根节点
  const handleArtworkRootDrop = async (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOverNodeId(null)

    const draggedFileId = e.dataTransfer.getData('text/plain')
    if (draggedFileId && fileTree) {
      // 移动到根节点（fileTree.id 是根节点ID）
      const success = await handleFileMove(draggedFileId, fileTree.id)
      if (success) {
        console.log('✅ 文件移动到作品根级别成功')
      }
    }
  }

  const handleArtworkRootDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
    setDragOverNodeId('artwork-root')
  }

  const handleArtworkRootDragLeave = () => {
    setDragOverNodeId(null)
  }

  return (
    <div className={`file-tree-panel ${className}`}>
      {/* 作品根节点 */}
      <div
        className={`
          flex items-center gap-2 px-3 py-2 mb-1 cursor-pointer
          bg-gradient-to-r from-amber-900/15 to-amber-800/5
          border-l-3 border-amber-600/50 rounded-r-lg mx-1 mt-1
          hover:from-amber-900/25 hover:to-amber-800/15
          hover:border-amber-500 transition-all duration-200
          ${dragOverNodeId === 'artwork-root' ? 'bg-amber-500/20 border-amber-500 border-2 border-dashed' : ''}
        `}
        onDrop={handleArtworkRootDrop}
        onDragOver={handleArtworkRootDragOver}
        onDragLeave={handleArtworkRootDragLeave}
        onContextMenu={(e) => {
          e.preventDefault()
          if (fileTree) {
            setContextMenu({
              show: true,
              position: { x: e.clientX, y: e.clientY },
              node: fileTree
            })
          }
        }}
      >
        {/* 展开/折叠按钮 */}
        <button
          className="p-1 hover:bg-amber-500/20 rounded-md transition-all duration-200"
          onClick={(e) => {
            e.stopPropagation()
            setIsArtworkExpanded(!isArtworkExpanded)
          }}
        >
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            className={`transition-transform duration-200 ${isArtworkExpanded ? 'rotate-90' : ''}`}
          >
            <path
              d="M4 2l4 4-4 4"
              fill="none"
              stroke="#D4AF37"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              style={{
                filter: 'drop-shadow(0 0 2px rgba(212, 175, 55, 0.4))'
              }}
            />
          </svg>
        </button>

        {/* 作品图标 */}
        <div className="flex-shrink-0">
          <ArtworkIcon />
        </div>

        {/* 作品标题 */}
        <span className="flex-1 text-amber-100 font-medium text-sm font-handwritten truncate">
          {artworkInfo?.title || artworkTitle}
        </span>

        {/* 拖拽提示 */}
        {dragOverNodeId === 'artwork-root' && (
          <div className="text-xs text-amber-300 opacity-75">
            移动到根级别
          </div>
        )}
      </div>

      {/* 文件树内容 - 根据作品展开状态显示 */}
      {isArtworkExpanded && (
        <div className="overflow-auto h-full">
          {fileTree.children && fileTree.children.map((child) => (
            <FileTreeItem
              key={child.id}
              node={child}
              level={1}
              isSelected={selectedNodeId === child.id}
              onSelect={handleNodeSelect}
              onToggle={handleNodeToggle}
              onContextMenu={handleContextMenu}
              onRename={handleRename}
              expandedNodes={expandedNodes}
              onFileMove={handleFileMove}
              draggedNodeId={draggedNodeId}
              dragOverNodeId={dragOverNodeId}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              messageTokenStatus={messageTokenStatus}
              isGeneratingTokens={isGeneratingTokens}
            />
          ))}
        </div>
      )}

      {/* 右键菜单 */}
      {contextMenu.show && contextMenu.node && (
        <ContextMenu
          items={getContextMenuItems(contextMenu.node)}
          position={contextMenu.position}
          onClose={closeContextMenu}
        />
      )}

      {/* 自定义确认对话框 */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.title}
        message={confirmDialog.message}
        type={confirmDialog.type}
        onConfirm={confirmDialog.onConfirm}
        onCancel={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
      />
    </div>
  )
}