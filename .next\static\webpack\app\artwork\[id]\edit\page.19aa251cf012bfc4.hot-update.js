"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/EditorPanel/index.tsx":
/*!**********************************************!*\
  !*** ./src/components/EditorPanel/index.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @monaco-editor/react */ \"(app-pages-browser)/./node_modules/@monaco-editor/react/dist/index.mjs\");\n/* harmony import */ var _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/editorDiffService */ \"(app-pages-browser)/./src/services/editorDiffService.ts\");\n/* harmony import */ var _components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DiffViewer/DiffViewerContainer */ \"(app-pages-browser)/./src/components/DiffViewer/DiffViewerContainer.tsx\");\n/* harmony import */ var _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/editorIntegration */ \"(app-pages-browser)/./src/services/editorIntegration.ts\");\n/* harmony import */ var _components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/MindMapRenderer */ \"(app-pages-browser)/./src/components/MindMapRenderer/index.tsx\");\n/* harmony import */ var _utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/fileTypeUtils */ \"(app-pages-browser)/./src/utils/fileTypeUtils.ts\");\n/* harmony import */ var _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/MindMapRenderer/managers/MarkdownConverter */ \"(app-pages-browser)/./src/components/MindMapRenderer/managers/MarkdownConverter.ts\");\n/**\r\n * 编辑器面板组件\r\n * 基于Monaco Editor的文本编辑器，支持Markdown语法高亮\r\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 默认编辑器设置 - 优化文字创作体验\nconst DEFAULT_SETTINGS = {\n    fontSize: 16,\n    fontWeight: 400,\n    fontFamily: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, \"Times New Roman\", serif',\n    theme: \"dark\",\n    wordWrap: true,\n    wordWrapColumn: 56,\n    showRulers: false,\n    rulers: [\n        56\n    ],\n    showLineNumbers: false,\n    enablePreview: false,\n    tabSize: 2,\n    insertSpaces: true,\n    autoSave: true,\n    autoSaveDelay: 1000\n};\nconst EditorPanel = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { file, onContentChange, onSettingsChange, onFileRename, settings = DEFAULT_SETTINGS, className = \"\", onAutoAssociationToggle, autoAssociationEnabled: propAutoAssociationEnabled, artworkId, onOpenDetailedDiff, onLayoutChange } = param;\n    _s();\n    const [editorContent, setEditorContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRenamingFile, setIsRenamingFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [renamingValue, setRenamingValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [availableFonts, setAvailableFonts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 自动关联功能状态\n    const [autoAssociationEnabled, setAutoAssociationEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propAutoAssociationEnabled !== null && propAutoAssociationEnabled !== void 0 ? propAutoAssociationEnabled : true);\n    // diff功能状态\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [diffData, setDiffData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [diffLoading, setDiffLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [diffError, setDiffError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 表格渲染功能状态\n    const [tableRenderingEnabled, setTableRenderingEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [tableIntegration, setTableIntegration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 思维导图相关状态\n    const [mindMapData, setMindMapData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapParseResult, setMindMapParseResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mindMapLoading, setMindMapLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mindMapError, setMindMapError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Markdown 转换器实例\n    const [markdownConverter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _components_MindMapRenderer_managers_MarkdownConverter__WEBPACK_IMPORTED_MODULE_8__.MarkdownConverter({\n            debug: \"development\" === \"development\"\n        }));\n    // 思维导图模式状态（针对 Markdown 文件）\n    const [isMindMapMode, setIsMindMapMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mindMapRendererRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null) // 新增：MindMapRenderer 引用\n    ;\n    // 🔧 统一的定时器管理 - 只保留一个定时器用于自动保存\n    const saveTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const fileNameInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const tableIntegrationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 动态计算文件类型（基于文件名后缀）\n    const currentFileType = file ? (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.getFileTypeFromName)(file.name) : \"text\";\n    // 🔧 统一的定时器清理函数\n    const clearSaveTimer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (saveTimeoutRef.current) {\n            clearTimeout(saveTimeoutRef.current);\n            saveTimeoutRef.current = undefined;\n            console.log(\"\\uD83D\\uDD12 清除保存定时器\");\n        }\n    }, []);\n    // 当文件变化时更新编辑器内容\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _mindMapRendererRef_current;\n        // 🔧 文件切换时清除所有定时器\n        clearSaveTimer();\n        // 🔧 清理思维导图组件的所有定时器和资源\n        if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.cleanup) {\n            mindMapRendererRef.current.cleanup();\n            console.log(\"\\uD83E\\uDDF9 清理思维导图组件资源\");\n        }\n        if (file) {\n            // 设置编辑器内容\n            setEditorContent(file.content || \"\");\n            // 检查文件类型并处理思维导图模式\n            const isMarkdown = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name);\n            const isMindMap = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name);\n            if (isMindMap) {\n                // .mindmap 文件始终使用思维导图模式\n                setIsMindMapMode(true);\n                parseMindMapContent(file);\n            } else if (isMarkdown) {\n                // .md 文件检查是否有思维导图模式的历史状态\n                const savedMode = localStorage.getItem(\"mindMapMode_\".concat(file.id));\n                const shouldUseMindMapMode = savedMode === \"true\";\n                setIsMindMapMode(shouldUseMindMapMode);\n                if (shouldUseMindMapMode) {\n                    parseMindMapContent(file);\n                } else {\n                    // 清除思维导图状态\n                    setMindMapData(null);\n                    setMindMapParseResult(null);\n                    setMindMapError(null);\n                }\n            } else {\n                // 其他文件类型，清除思维导图状态\n                setIsMindMapMode(false);\n                setMindMapData(null);\n                setMindMapParseResult(null);\n                setMindMapError(null);\n            }\n        } else {\n            setEditorContent(\"\");\n            setIsMindMapMode(false);\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n    }, [\n        file === null || file === void 0 ? void 0 : file.id,\n        file === null || file === void 0 ? void 0 : file.name\n    ]) // 只监听文件ID和名称变化，不监听内容变化\n    ;\n    // 将思维导图数据转换为Markdown格式\n    const convertMindMapToMarkdown = (data)=>{\n        const convertNode = function(node) {\n            let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n            const prefix = \"#\".repeat(level);\n            let result = \"\".concat(prefix, \" \").concat(node.data.text, \"\\n\\n\");\n            if (node.children && node.children.length > 0) {\n                for (const child of node.children){\n                    result += convertNode(child, level + 1);\n                }\n            }\n            return result;\n        };\n        return convertNode(data).trim();\n    };\n    // 解析思维导图内容\n    const parseMindMapContent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        setMindMapLoading(true);\n        setMindMapError(null);\n        try {\n            let result;\n            // 统一使用 MarkdownConverter，消除双解析器冲突\n            if ((0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode) {\n                const fileType = (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) ? \".mindmap\" : \".md\";\n                console.log(\"\\uD83D\\uDD04 使用统一 MarkdownConverter 解析 \".concat(fileType, \" 文件\"));\n                const conversionResult = await markdownConverter.convertFromMarkdown(file.content);\n                if (conversionResult.success && conversionResult.data) {\n                    result = {\n                        success: true,\n                        data: conversionResult.data,\n                        originalContent: file.content,\n                        processingTime: 0\n                    };\n                    console.log(\"✅ \".concat(fileType, \" 文件解析成功\"));\n                } else {\n                    throw new Error(conversionResult.error || \"\".concat(fileType, \"文件转换失败\"));\n                }\n            } else {\n                throw new Error(\"不支持的文件类型或模式\");\n            }\n            if (result.success && result.data) {\n                // 批量更新状态，减少重渲染\n                setMindMapData(result.data);\n                setMindMapParseResult(result);\n                console.log(\"✅ 思维导图解析成功:\", result.data);\n            } else {\n                setMindMapError(result.error || \"思维导图解析失败\");\n                console.error(\"❌ 思维导图解析失败:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"思维导图解析错误:\", error);\n            setMindMapError(error instanceof Error ? error.message : \"思维导图解析出错\");\n        } finally{\n            setMindMapLoading(false);\n        }\n    }, [\n        markdownConverter,\n        isMindMapMode\n    ]) // 添加依赖项\n    ;\n    // 切换思维导图模式（仅对 Markdown 文件有效）\n    const toggleMindMapMode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!file || !(0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name)) return;\n        // 🔧 视图切换时清除所有定时器\n        clearSaveTimer();\n        const newMode = !isMindMapMode;\n        setIsMindMapMode(newMode);\n        // 保存模式状态到 localStorage\n        localStorage.setItem(\"mindMapMode_\".concat(file.id), newMode.toString());\n        if (newMode) {\n            // 切换到思维导图模式，解析当前内容\n            parseMindMapContent(file);\n        } else {\n            // 切换到编辑器模式，清除思维导图状态\n            setMindMapData(null);\n            setMindMapParseResult(null);\n            setMindMapError(null);\n        }\n        console.log(\"\\uD83D\\uDD04 思维导图模式切换:\", newMode ? \"开启\" : \"关闭\");\n    }, [\n        file,\n        isMindMapMode,\n        parseMindMapContent\n    ]);\n    // 处理布局变化的回调函数\n    const handleLayoutChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        console.log(\"\\uD83D\\uDD04 接收到布局变化通知，更新画布尺寸\");\n        // 延迟执行，确保DOM布局更新完成\n        setTimeout(()=>{\n            var _mindMapRendererRef_current;\n            if ((_mindMapRendererRef_current = mindMapRendererRef.current) === null || _mindMapRendererRef_current === void 0 ? void 0 : _mindMapRendererRef_current.resize) {\n                mindMapRendererRef.current.resize();\n            }\n        }, 100);\n    }, []);\n    // 🔧 统一的保存处理函数\n    const handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((content)=>{\n        if (!settings.autoSave || !onContentChange) return;\n        clearSaveTimer();\n        saveTimeoutRef.current = setTimeout(()=>{\n            onContentChange(content);\n        }, settings.autoSaveDelay);\n    }, [\n        settings.autoSave,\n        settings.autoSaveDelay,\n        onContentChange,\n        clearSaveTimer\n    ]);\n    // 处理编辑器内容变化\n    const handleEditorChange = (value)=>{\n        let content;\n        // 如果是思维导图数据对象，转换为JSON字符串\n        if (typeof value === \"object\" && value !== null) {\n            content = JSON.stringify(value, null, 2);\n        } else {\n            content = value || \"\";\n        }\n        setEditorContent(content);\n        handleSave(content);\n    };\n    // 处理思维导图数据变更\n    const handleMindMapDataChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((data)=>{\n        console.log(\"\\uD83D\\uDCCA 思维导图数据变更:\", data);\n        // 🔧 验证当前文件状态，防止跨文件保存\n        if (!file || !isMindMapMode) {\n            console.log(\"\\uD83D\\uDD12 文件状态已变化或非思维导图模式，跳过保存\");\n            return;\n        }\n        // 🔧 获取当前文件ID作为基准，防止文件切换时的竞态条件\n        const currentFileId = file.id;\n        // 将思维导图数据转换为Markdown格式并保存\n        if (data && onContentChange) {\n            const markdownContent = convertMindMapToMarkdown(data);\n            // 🔧 延迟验证机制，防止文件切换过程中的错误保存\n            setTimeout(()=>{\n                // 再次验证文件ID一致性\n                if (!file || file.id !== currentFileId || !isMindMapMode) {\n                    console.log(\"\\uD83D\\uDD12 文件已切换或模式已变化，取消保存操作\");\n                    return;\n                }\n                handleSave(markdownContent);\n                console.log(\"\\uD83D\\uDCBE 思维导图数据已转换为Markdown并自动保存\");\n            }, 10);\n        }\n    }, [\n        file,\n        isMindMapMode,\n        onContentChange,\n        convertMindMapToMarkdown,\n        handleSave\n    ]);\n    // 处理编辑器挂载\n    const handleEditorDidMount = (editor, monaco)=>{\n        editorRef.current = editor;\n        // 初始化表格渲染功能（仅对Markdown文件）\n        if (currentFileType === \"markdown\") {\n            try {\n                const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                integration.initialize();\n                // 保存集成实例到状态和编辑器\n                setTableIntegration(integration);\n                editor.__tableIntegration = integration;\n                console.log(\"✅ 表格渲染功能已集成到编辑器\");\n            } catch (error) {\n                console.error(\"❌ 表格渲染功能初始化失败:\", error);\n                setTableIntegration(null);\n            }\n        }\n        // 配置文字创作专用主题 - 温暖舒适的阅读体验\n        monaco.editor.defineTheme(\"miyazaki-writing\", {\n            base: \"vs-dark\",\n            inherit: true,\n            rules: [\n                // Markdown 语法高亮优化\n                {\n                    token: \"keyword.md\",\n                    foreground: \"#F4A460\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"string.md\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"emphasis.md\",\n                    foreground: \"#DEB887\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"strong.md\",\n                    foreground: \"#F0E68C\",\n                    fontStyle: \"bold\"\n                },\n                {\n                    token: \"variable.md\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"string.link.md\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"comment.md\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"number.md\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"delimiter.md\",\n                    foreground: \"#D2B48C\"\n                },\n                // 通用语法\n                {\n                    token: \"comment\",\n                    foreground: \"#8FBC8F\",\n                    fontStyle: \"italic\"\n                },\n                {\n                    token: \"keyword\",\n                    foreground: \"#F4A460\"\n                },\n                {\n                    token: \"string\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.double\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"string.quoted.single\",\n                    foreground: \"#98FB98\"\n                },\n                {\n                    token: \"number\",\n                    foreground: \"#DDA0DD\"\n                },\n                {\n                    token: \"regexp\",\n                    foreground: \"#FF6B6B\"\n                },\n                {\n                    token: \"type\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"class\",\n                    foreground: \"#98D8C8\"\n                },\n                {\n                    token: \"function\",\n                    foreground: \"#F0E68C\"\n                },\n                {\n                    token: \"variable\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"constant\",\n                    foreground: \"#87CEEB\"\n                },\n                {\n                    token: \"property\",\n                    foreground: \"#E6D7B7\"\n                },\n                {\n                    token: \"operator\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.parenthesis\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.square\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"delimiter.curly\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.bracket\",\n                    foreground: \"#FFD700\"\n                },\n                {\n                    token: \"punctuation.definition\",\n                    foreground: \"#FFD700\"\n                } // 定义标点\n            ],\n            colors: {\n                // 背景色 - 深色但温暖\n                \"editor.background\": \"#1A1A1A\",\n                \"editor.foreground\": \"#E6D7B7\",\n                // 行号\n                \"editorLineNumber.foreground\": \"#6B5B73\",\n                \"editorLineNumber.activeForeground\": \"#8B7B8B\",\n                // 选择和高亮\n                \"editor.selectionBackground\": \"#4A4A2A\",\n                \"editor.selectionHighlightBackground\": \"#3A3A1A26\",\n                \"editor.wordHighlightBackground\": \"#4A4A2A88\",\n                \"editor.wordHighlightStrongBackground\": \"#5A5A3A88\",\n                // 光标和当前行\n                \"editorCursor.foreground\": \"#F4A460\",\n                \"editor.lineHighlightBackground\": \"#2A2A1A\",\n                // 滚动条\n                \"scrollbarSlider.background\": \"#4A4A2A66\",\n                \"scrollbarSlider.hoverBackground\": \"#5A5A3A88\",\n                \"scrollbarSlider.activeBackground\": \"#6A6A4A\",\n                // 边框和分割线\n                \"editorWidget.border\": \"#F4A46033\",\n                \"editorHoverWidget.background\": \"#2A2A1A\",\n                \"editorHoverWidget.border\": \"#F4A46066\",\n                // 建议框\n                \"editorSuggestWidget.background\": \"#2A2A1A\",\n                \"editorSuggestWidget.border\": \"#F4A46033\",\n                \"editorSuggestWidget.foreground\": \"#E6D7B7\",\n                \"editorSuggestWidget.selectedBackground\": \"#4A4A2A\",\n                // 查找框\n                \"editorFindMatch.background\": \"#5A5A3A88\",\n                \"editorFindMatchHighlight.background\": \"#4A4A2A66\",\n                \"editorFindRangeHighlight.background\": \"#3A3A1A33\"\n            }\n        });\n        // 设置主题\n        monaco.editor.setTheme(\"miyazaki-writing\");\n    };\n    // 处理设置变化\n    const handleSettingsChange = (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        if (onSettingsChange) {\n            onSettingsChange(updatedSettings);\n        }\n    };\n    // 🔧 直接处理字符数变更，不使用防抖\n    const handleWordWrapColumnChange = (value)=>{\n        handleSettingsChange({\n            wordWrapColumn: value\n        });\n    };\n    // 处理换行模式变更\n    const handleWrapModeChange = (mode)=>{\n        if (mode === \"wordWrapColumn\") {\n            // 切换到按字符数换行时，确保有默认字符数\n            const wordWrapColumn = settings.wordWrapColumn || 56;\n            handleSettingsChange({\n                wordWrap: \"wordWrapColumn\",\n                wordWrapColumn: wordWrapColumn\n            });\n        } else {\n            handleSettingsChange({\n                wordWrap: mode === \"on\"\n            });\n        }\n    };\n    // 处理标尺显示切换\n    const handleRulersToggle = ()=>{\n        const newShowRulers = !settings.showRulers;\n        // 如果开启标尺且没有设置标尺位置，使用当前字符数作为默认位置\n        const rulers = newShowRulers ? settings.rulers || [\n            settings.wordWrapColumn || 56\n        ] : settings.rulers;\n        handleSettingsChange({\n            showRulers: newShowRulers,\n            rulers: rulers\n        });\n    };\n    // 加载可用字体\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAvailableFonts = async ()=>{\n            try {\n                const { FontService } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_services_fontService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/services/fontService */ \"(app-pages-browser)/./src/services/fontService.ts\"));\n                const fontService = FontService.getInstance();\n                const result = await fontService.getAllFonts();\n                if (result.success && result.data) {\n                    const fonts = result.data.map((font)=>({\n                            name: font.name,\n                            family: font.family\n                        }));\n                    // 添加系统默认字体\n                    const systemFonts = [\n                        {\n                            name: \"思源宋体\",\n                            family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                        },\n                        {\n                            name: \"思源黑体\",\n                            family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                        },\n                        {\n                            name: \"Monaco\",\n                            family: 'Monaco, Consolas, \"Courier New\", monospace'\n                        },\n                        {\n                            name: \"Georgia\",\n                            family: 'Georgia, \"Times New Roman\", serif'\n                        },\n                        {\n                            name: \"Arial\",\n                            family: \"Arial, sans-serif\"\n                        }\n                    ];\n                    setAvailableFonts([\n                        ...systemFonts,\n                        ...fonts\n                    ]);\n                }\n            } catch (error) {\n                console.error(\"加载字体列表失败:\", error);\n                // 使用默认字体列表\n                setAvailableFonts([\n                    {\n                        name: \"思源宋体\",\n                        family: '\"Noto Serif SC\", \"Source Han Serif SC\", \"思源宋体\", Georgia, serif'\n                    },\n                    {\n                        name: \"思源黑体\",\n                        family: '\"Noto Sans SC\", \"Source Han Sans SC\", \"思源黑体\", Arial, sans-serif'\n                    },\n                    {\n                        name: \"Monaco\",\n                        family: 'Monaco, Consolas, \"Courier New\", monospace'\n                    }\n                ]);\n            }\n        };\n        loadAvailableFonts();\n    }, []);\n    // 自动关联功能状态管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 从localStorage加载自动关联状态\n        const saved = localStorage.getItem(\"autoAssociationEnabled\");\n        if (saved !== null) {\n            const savedValue = JSON.parse(saved);\n            setAutoAssociationEnabled(savedValue);\n        }\n    }, []);\n    // 同步外部传入的自动关联状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (propAutoAssociationEnabled !== undefined) {\n            setAutoAssociationEnabled(propAutoAssociationEnabled);\n        }\n    }, [\n        propAutoAssociationEnabled\n    ]);\n    // 处理自动关联开关切换\n    const toggleAutoAssociation = ()=>{\n        const newValue = !autoAssociationEnabled;\n        setAutoAssociationEnabled(newValue);\n        localStorage.setItem(\"autoAssociationEnabled\", JSON.stringify(newValue));\n        if (onAutoAssociationToggle) {\n            onAutoAssociationToggle(newValue);\n        }\n        console.log(\"\\uD83C\\uDF9B️ 自动关联功能\", newValue ? \"开启\" : \"关闭\");\n    };\n    // 处理打开详细差异对比\n    const handleOpenDetailedDiff = async (diffRequest)=>{\n        if (!artworkId || !file) {\n            console.error(\"❌ 缺少必要参数：artworkId 或 file\");\n            return;\n        }\n        try {\n            setDiffLoading(true);\n            setDiffError(null);\n            console.log(\"\\uD83D\\uDD04 开始计算diff数据:\", {\n                artworkId,\n                filePath: diffRequest.filePath,\n                operation: diffRequest.operation,\n                contentLength: diffRequest.content.length\n            });\n            // 使用EditorDiffCalculator计算diff数据\n            const diffCalculator = _services_editorDiffService__WEBPACK_IMPORTED_MODULE_3__.EditorDiffCalculator.getInstance();\n            const result = await diffCalculator.calculateEditorDiff(artworkId, diffRequest.filePath, diffRequest.content, diffRequest.operation);\n            if (result.success && result.data) {\n                console.log(\"✅ 成功生成diff数据:\", {\n                    additions: result.data.additions,\n                    deletions: result.data.deletions,\n                    modifications: result.data.modifications,\n                    hunksCount: result.data.hunks.length\n                });\n                setDiffData(result.data);\n                setViewMode(\"diff\");\n            } else {\n                throw new Error(result.error || \"生成diff数据失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 生成diff数据失败:\", error);\n            setDiffError(error instanceof Error ? error.message : \"未知错误\");\n        } finally{\n            setDiffLoading(false);\n        }\n    };\n    // 使用useImperativeHandle暴露方法给父组件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            handleOpenDetailedDiff,\n            handleLayoutChange\n        }), [\n        handleOpenDetailedDiff,\n        handleLayoutChange\n    ]);\n    // 处理关闭diff视图\n    const handleCloseDiff = ()=>{\n        setViewMode(\"normal\");\n        setDiffData(null);\n        setDiffError(null);\n    };\n    // 处理应用diff更改\n    const handleApplyDiffChanges = (content)=>{\n        if (onContentChange) {\n            onContentChange(content);\n            setEditorContent(content);\n        }\n        handleCloseDiff();\n        console.log(\"✅ 已应用diff更改\");\n    };\n    // 处理diff错误\n    const handleDiffError = (error)=>{\n        setDiffError(error);\n        console.error(\"❌ Diff视图错误:\", error);\n    };\n    // 🔧 清理定时器和表格集成\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            clearSaveTimer();\n            // 清理表格渲染集成\n            const editor = editorRef.current;\n            const integration = editor === null || editor === void 0 ? void 0 : editor.__tableIntegration;\n            if (integration) {\n                integration.dispose();\n                delete editor.__tableIntegration;\n            }\n            // 清理 Markdown 转换器\n            if (markdownConverter) {\n                markdownConverter.destroy();\n            }\n        };\n    }, [\n        clearSaveTimer\n    ]);\n    // 如果没有文件，显示欢迎界面\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                        children: \"选择一个文件开始编辑\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 text-sm font-handwritten\",\n                        children: \"从左侧文件树中选择文件，或创建新文件\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 645,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n            lineNumber: 644,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-4 py-2 bg-gray-800/50 border-b border-amber-500/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 rounded-full bg-amber-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isRenamingFile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ref: fileNameInputRef,\n                                        type: \"text\",\n                                        value: renamingValue,\n                                        onChange: (e)=>setRenamingValue(e.target.value),\n                                        onBlur: ()=>{\n                                            if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                onFileRename(file.id, renamingValue.trim());\n                                            }\n                                            setIsRenamingFile(false);\n                                        },\n                                        onKeyDown: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                if (renamingValue.trim() && renamingValue !== file.name && onFileRename) {\n                                                    onFileRename(file.id, renamingValue.trim());\n                                                }\n                                                setIsRenamingFile(false);\n                                            } else if (e.key === \"Escape\") {\n                                                setRenamingValue(file.name);\n                                                setIsRenamingFile(false);\n                                            }\n                                        },\n                                        className: \"text-sm font-handwritten text-amber-200 bg-gray-800 border border-amber-500/50 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-handwritten text-amber-200 cursor-pointer hover:text-amber-100 transition-colors duration-200 px-1 py-0.5 rounded hover:bg-amber-500/10\",\n                                        onClick: ()=>{\n                                            setRenamingValue(file.name);\n                                            setIsRenamingFile(true);\n                                        },\n                                        title: \"点击编辑文件名\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 664,\n                                columnNumber: 11\n                            }, undefined),\n                            file.isDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 rounded-full bg-orange-500\",\n                                title: \"未保存的更改\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-400 px-2 py-1 bg-gray-700/50 rounded\",\n                                        children: currentFileType\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-3 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(isMindMapMode ? \"text-purple-200 bg-purple-600/30 border-purple-500/70 hover:text-purple-100 hover:bg-purple-600/40 shadow-lg shadow-purple-500/20\" : \"text-blue-200 bg-blue-600/30 border-blue-500/70 hover:text-blue-100 hover:bg-blue-600/40 shadow-lg shadow-blue-500/20\"),\n                                        title: \"思维导图模式 (\".concat(isMindMapMode ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: isMindMapMode ? // 思维导图图标（开启状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 23\n                                                    }, undefined) : // 编辑器图标（关闭状态）\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 731,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: isMindMapMode ? \"思维导图\" : \"编辑器\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMindMapFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-3 py-2 bg-green-600/20 border border-green-500/50 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"currentColor\",\n                                                className: \"text-green-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12,2A2,2 0 0,1 14,4A2,2 0 0,1 12,6A2,2 0 0,1 10,4A2,2 0 0,1 12,2M21,9V7L15,1H5A2,2 0 0,0 3,3V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V9M19,9H14V4H5V19H19V9Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-green-200 font-medium\",\n                                                children: \"自动解析\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (currentFileType !== \"markdown\" || isMindMapMode) {\n                                                if (isMindMapMode) {\n                                                    alert(\"表格渲染功能在思维导图模式下不可用！\\n请切换到编辑器模式来使用此功能。\");\n                                                } else {\n                                                    alert(\"表格渲染功能仅支持Markdown文件！\\n请打开.md文件来使用此功能。\");\n                                                }\n                                                return;\n                                            }\n                                            console.log(\"\\uD83D\\uDD04 表格渲染按钮被点击\");\n                                            console.log(\"\\uD83D\\uDCCA 当前表格集成状态:\", tableIntegration);\n                                            console.log(\"\\uD83D\\uDCCA 当前渲染状态:\", tableRenderingEnabled);\n                                            if (tableIntegration) {\n                                                try {\n                                                    tableIntegration.toggleTableRendering();\n                                                    const newState = tableIntegration.isTableRenderingEnabled();\n                                                    setTableRenderingEnabled(newState);\n                                                    console.log(\"✅ 表格渲染状态已切换为:\", newState);\n                                                } catch (error) {\n                                                    console.error(\"❌ 切换表格渲染失败:\", error);\n                                                }\n                                            } else {\n                                                console.warn(\"⚠️ 表格集成未初始化，尝试重新初始化...\");\n                                                const editor = editorRef.current;\n                                                if (editor) {\n                                                    try {\n                                                        const integration = new _services_editorIntegration__WEBPACK_IMPORTED_MODULE_5__.EditorIntegration(editor);\n                                                        integration.initialize();\n                                                        setTableIntegration(integration);\n                                                        editor.__tableIntegration = integration;\n                                                        console.log(\"✅ 表格集成重新初始化成功\");\n                                                    } catch (error) {\n                                                        console.error(\"❌ 表格集成重新初始化失败:\", error);\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        disabled: currentFileType !== \"markdown\" || isMindMapMode,\n                                        className: \"px-4 py-2 rounded-md transition-all duration-200 text-sm font-bold border-2 \".concat(currentFileType !== \"markdown\" || isMindMapMode ? \"text-gray-500 bg-gray-800/50 border-gray-600/50 cursor-not-allowed\" : tableRenderingEnabled ? \"text-green-200 bg-green-600/30 border-green-500/70 hover:text-green-100 hover:bg-green-600/40 shadow-lg shadow-green-500/20\" : \"text-amber-200 bg-amber-600/30 border-amber-500/70 hover:text-amber-100 hover:bg-amber-600/40 shadow-lg shadow-amber-500/20\"),\n                                        title: currentFileType !== \"markdown\" ? \"表格渲染功能仅支持Markdown文件\" : isMindMapMode ? \"表格渲染功能在思维导图模式下不可用\" : \"表格渲染功能 (\".concat(tableRenderingEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"18\",\n                                                    height: \"18\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M3,3H21V21H3V3M5,5V19H19V5H5M7,7H17V9H7V7M7,11H17V13H7V11M7,15H17V17H7V15Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                        lineNumber: 818,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: currentFileType !== \"markdown\" ? \"表格功能\" : tableRenderingEnabled ? \"表格 ON\" : \"表格 OFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAutoAssociation,\n                                className: \"p-2 rounded-md transition-all duration-200 \".concat(autoAssociationEnabled ? \"text-blue-400 bg-blue-500/10 hover:text-blue-300 hover:bg-blue-500/20\" : \"text-gray-400 hover:text-amber-400 hover:bg-amber-500/10\"),\n                                title: \"自动关联当前编辑文件到AI助手 (\".concat(autoAssociationEnabled ? \"已开启\" : \"已关闭\", \")\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: autoAssociationEnabled ? // 开启状态：循环箭头图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12,6V9L16,5L12,1V4A8,8 0 0,0 4,12C4,13.57 4.46,15.03 5.24,16.26L6.7,14.8C6.25,13.97 6,13 6,12A6,6 0 0,1 12,6M18.76,7.74L17.3,9.2C17.74,10.04 18,11 18,12A6,6 0 0,1 12,18V15L8,19L12,23V20A8,8 0 0,0 20,12C20,10.43 19.54,8.97 18.76,7.74Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 17\n                                    }, undefined) : // 关闭状态：断开的链接图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M17,7H22V9H19V12C19,13.1 18.1,14 17,14H14L12,16H17C19.21,16 21,14.21 21,12V9H22V7H17M7,7C5.79,7 4.8,7.79 4.8,8.8V11.2C4.8,12.21 5.79,13 7,13H10L12,11H7C6.45,11 6,10.55 6,10V9C6,8.45 6.45,8 7,8H12V7H7M2,2L20,20L18.73,21.27L15,17.54C14.28,17.84 13.5,18 12.67,18H7C4.79,18 3,16.21 3,14V11C3,9.79 3.79,8.8 4.8,8.8V8C4.8,6.79 5.79,6 7,6H8.73L2,2Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(!showSettings),\n                                className: \"p-2 text-gray-400 hover:text-amber-400 hover:bg-amber-500/10 rounded-md transition-all duration-200\",\n                                title: \"编辑器设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"16\",\n                                    height: \"16\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 851,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 711,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 661,\n                columnNumber: 7\n            }, undefined),\n            showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 bg-gray-900/50 border-b border-amber-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体选择\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 869,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.fontFamily,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontFamily: e.target.value\n                                        }),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: availableFonts.map((font, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: font.family,\n                                            children: font.name\n                                        }, index, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 878,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 868,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体大小\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"10\",\n                                    max: \"24\",\n                                    value: settings.fontSize,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontSize: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.fontSize,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 898,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"字体粗细\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"100\",\n                                    max: \"900\",\n                                    step: \"100\",\n                                    value: settings.fontWeight,\n                                    onChange: (e)=>handleSettingsChange({\n                                            fontWeight: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 906,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: settings.fontWeight\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 902,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                                    onChange: (e)=>handleWrapModeChange(e.target.value),\n                                    className: \"w-full px-2 py-1 text-xs bg-gray-700 border border-gray-600 rounded-md text-amber-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"off\",\n                                            children: \"不换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"on\",\n                                            children: \"自动换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"wordWrapColumn\",\n                                            children: \"按字符数换行\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 930,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 923,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 919,\n                            columnNumber: 13\n                        }, undefined),\n                        settings.wordWrap === \"wordWrapColumn\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"换行字符数\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"40\",\n                                    max: \"120\",\n                                    value: settings.wordWrapColumn || 56,\n                                    onChange: (e)=>handleWordWrapColumnChange(parseInt(e.target.value)),\n                                    className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-400 mt-1\",\n                                    children: [\n                                        settings.wordWrapColumn || 56,\n                                        \"字符\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 948,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 936,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"标尺线\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRulersToggle,\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showRulers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showRulers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 953,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-handwritten text-amber-200 mb-1\",\n                                    children: \"行号显示\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 971,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleSettingsChange({\n                                            showLineNumbers: !settings.showLineNumbers\n                                        }),\n                                    className: \"w-full px-3 py-1 text-xs rounded-md transition-all duration-200 \".concat(settings.showLineNumbers ? \"bg-amber-500/20 text-amber-200 border border-amber-500/50\" : \"bg-gray-700 text-gray-400 border border-gray-600\"),\n                                    children: settings.showLineNumbers ? \"显示\" : \"隐藏\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 970,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 866,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 865,\n                columnNumber: 9\n            }, undefined),\n            viewMode === \"diff\" ? /* Diff视图模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: diffLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-purple-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-purple-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 997,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-purple-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 996,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在生成差异对比...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1000,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 995,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 994,\n                    columnNumber: 13\n                }, undefined) : diffError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⚠️\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1006,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"差异对比失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1007,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: diffError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1008,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCloseDiff,\n                                className: \"px-4 py-2 bg-amber-600/30 text-amber-200 rounded-md hover:bg-amber-600/40 transition-colors duration-200\",\n                                children: \"返回编辑器\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1009,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1005,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1004,\n                    columnNumber: 13\n                }, undefined) : diffData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiffViewer_DiffViewerContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    diffData: diffData,\n                    onClose: handleCloseDiff,\n                    onApplyChanges: handleApplyDiffChanges,\n                    onError: handleDiffError\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1018,\n                    columnNumber: 13\n                }, undefined) : null\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 992,\n                columnNumber: 9\n            }, undefined) : currentFileType === \"mindmap\" || (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && isMindMapMode ? /* 思维导图渲染模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: mindMapLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-4 text-green-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 border-3 border-green-400/30 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1033,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-8 h-8 border-3 border-green-400 border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1032,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-handwritten\",\n                                children: \"正在解析思维导图...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1031,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1030,\n                    columnNumber: 13\n                }, undefined) : mindMapError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-red-400 mb-2\",\n                                children: \"思维导图解析失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1043,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm mb-4\",\n                                children: mindMapError\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1044,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 justify-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>parseMindMapContent(file),\n                                        className: \"px-4 py-2 bg-green-600/30 text-green-200 rounded-md hover:bg-green-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"重新解析\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1046,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleMindMapMode,\n                                        className: \"px-4 py-2 bg-blue-600/30 text-blue-200 rounded-md hover:bg-blue-600/40 transition-colors duration-200 font-handwritten\",\n                                        children: \"切换到编辑器\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                        lineNumber: 1053,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1045,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-xs\",\n                                children: (0,_utils_fileTypeUtils__WEBPACK_IMPORTED_MODULE_7__.isMarkdownFile)(file.name) ? \"可以切换到编辑器模式查看原始 Markdown 内容\" : \"将显示原始文本内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1061,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1041,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1040,\n                    columnNumber: 13\n                }, undefined) : mindMapData ? /* 使用新的统一SimpleMindMap实现 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MindMapRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    ref: mindMapRendererRef,\n                    data: mindMapData,\n                    width: \"100%\",\n                    height: \"100%\",\n                    readonly: false,\n                    theme: \"dark\",\n                    loading: false,\n                    onRenderComplete: ()=>console.log(\"✅ 思维导图渲染完成\"),\n                    onNodeClick: (node)=>console.log(\"\\uD83D\\uDDB1️ 节点点击:\", node),\n                    onDataChange: handleMindMapDataChange,\n                    onSelectionComplete: (nodes)=>console.log(\"\\uD83D\\uDCE6 框选完成:\", nodes),\n                    className: \"mind-map-editor\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1071,\n                    columnNumber: 13\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"\\uD83E\\uDDE0\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1088,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-handwritten text-amber-200 mb-2\",\n                                children: \"思维导图文件\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1089,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: \"正在加载思维导图内容...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                lineNumber: 1090,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1087,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1086,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1028,\n                columnNumber: 9\n            }, undefined) : /* 普通文本编辑器模式 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_monaco_editor_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    height: \"100%\",\n                    language: currentFileType === \"markdown\" ? \"markdown\" : \"plaintext\",\n                    value: editorContent,\n                    onChange: handleEditorChange,\n                    onMount: handleEditorDidMount,\n                    loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4 text-amber-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-3 border-amber-400/30 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1108,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                            lineNumber: 1109,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-handwritten\",\n                                    children: \"正在加载编辑器...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                                    lineNumber: 1111,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                            lineNumber: 1106,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                        lineNumber: 1105,\n                        columnNumber: 15\n                    }, void 0),\n                    options: {\n                        fontSize: settings.fontSize,\n                        fontWeight: settings.fontWeight.toString(),\n                        fontFamily: settings.fontFamily,\n                        wordWrap: settings.wordWrap === \"wordWrapColumn\" ? \"wordWrapColumn\" : settings.wordWrap ? \"on\" : \"off\",\n                        wordWrapColumn: settings.wordWrapColumn || 56,\n                        rulers: settings.showRulers ? settings.rulers : [],\n                        lineNumbers: settings.showLineNumbers ? \"on\" : \"off\",\n                        minimap: {\n                            enabled: false\n                        },\n                        scrollBeyondLastLine: false,\n                        automaticLayout: true,\n                        tabSize: settings.tabSize,\n                        insertSpaces: settings.insertSpaces,\n                        renderWhitespace: \"selection\",\n                        cursorBlinking: \"smooth\",\n                        cursorSmoothCaretAnimation: \"on\",\n                        smoothScrolling: true,\n                        mouseWheelZoom: true,\n                        contextmenu: true,\n                        selectOnLineNumbers: true,\n                        roundedSelection: false,\n                        readOnly: false,\n                        cursorStyle: \"line\",\n                        glyphMargin: false,\n                        folding: true,\n                        showFoldingControls: \"mouseover\",\n                        matchBrackets: \"always\",\n                        renderLineHighlight: \"line\",\n                        scrollbar: {\n                            vertical: \"auto\",\n                            horizontal: \"auto\",\n                            useShadows: false,\n                            verticalHasArrows: false,\n                            horizontalHasArrows: false,\n                            verticalScrollbarSize: 10,\n                            horizontalScrollbarSize: 10\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                    lineNumber: 1098,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n                lineNumber: 1097,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\新写作\\\\src\\\\components\\\\EditorPanel\\\\index.tsx\",\n        lineNumber: 659,\n        columnNumber: 5\n    }, undefined);\n}, \"PG2CD78xuZgttSDmLmbn9ucF1P0=\")), \"PG2CD78xuZgttSDmLmbn9ucF1P0=\");\n_c1 = EditorPanel;\nEditorPanel.displayName = \"EditorPanel\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (EditorPanel);\nvar _c, _c1;\n$RefreshReg$(_c, \"EditorPanel$forwardRef\");\n$RefreshReg$(_c1, \"EditorPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/EditorPanel/index.tsx\n"));

/***/ })

});