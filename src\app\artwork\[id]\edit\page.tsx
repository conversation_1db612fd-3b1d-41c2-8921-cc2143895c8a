/**
 * 作品编辑页面
 * 三栏布局的编辑器界面
 */

'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { Artwork, EditorState, EditorSettings, FileTreeNode, EditorFile } from '@/types'
import { ArtworkService } from '@/services/artworkService'
import { FileTreeService } from '@/services/fileTreeService'
import { FileTreeEventService } from '@/services/fileTreeEventService'
import { ChatHistoryService } from '@/services/chatHistoryService'
import FileTreePanel from '@/components/FileTreePanel'
import EditorPanel from '@/components/EditorPanel'
import AIAssistantPanel from '@/components/AIAssistant'
import ResizableLayout from '@/components/ResizableLayout'

// 默认编辑器设置
const DEFAULT_EDITOR_SETTINGS: EditorSettings = {
  fontSize: 14,
  fontWeight: 400,
  fontFamily: 'Monaco, <PERSON>solas, "Courier New", monospace',
  theme: 'light',
  wordWrap: true,
  showLineNumbers: true,
  enablePreview: true,
  tabSize: 2,
  insertSpaces: true,
  autoSave: true,
  autoSaveDelay: 1000
}

// 默认文件树结构
const DEFAULT_FILE_TREE: FileTreeNode = {
  id: 'root',
  name: 'root',
  type: 'folder',
  parentId: null,
  path: '/',
  createdAt: Date.now(),
  updatedAt: Date.now(),
  isExpanded: true,
  children: [
    {
      id: 'folder-roles',
      name: '角色',
      type: 'folder',
      parentId: 'root',
      path: '/角色',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isExpanded: false,
      children: []
    },
    {
      id: 'folder-outline',
      name: '大纲',
      type: 'folder',
      parentId: 'root',
      path: '/大纲',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isExpanded: false,
      children: []
    },
    {
      id: 'folder-knowledge',
      name: '知识库',
      type: 'folder',
      parentId: 'root',
      path: '/知识库',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isExpanded: false,
      children: []
    }
  ]
}

interface ArtworkEditorPageProps {
  params: {
    id: string
  }
}

export default function ArtworkEditorPage() {
  const params = useParams()
  const router = useRouter()
  const artworkId = params?.id as string

  // 编辑器状态
  const [editorState, setEditorState] = useState<EditorState>({
    currentFile: null,
    fileTree: DEFAULT_FILE_TREE,
    aiHistory: [],
    settings: DEFAULT_EDITOR_SETTINGS,
    isLoading: true,
    unsavedChanges: false,
    error: null
  })

  // 文件树刷新状态已移除，现在使用事件系统自动刷新

  // 作品数据
  const [artwork, setArtwork] = useState<Artwork | null>(null)

  // 自动关联功能状态
  const [autoAssociationEnabled, setAutoAssociationEnabled] = useState(true)
  const chatHistoryService = ChatHistoryService.getInstance()

  // 自动关联状态反馈
  const [autoAssociationStatus, setAutoAssociationStatus] = useState<{
    type: 'success' | 'error' | 'processing' | 'idle'
    message: string
    timestamp?: number
  }>({ type: 'idle', message: '' })

  // 监听当前文件变化，实现自动关联
  useEffect(() => {
    if (autoAssociationEnabled && editorState.currentFile) {
      const fileId = editorState.currentFile.id
      const fileName = editorState.currentFile.name
      
      console.log('🔄 自动关联当前编辑文件:', fileId)
      
      // 🔧 静默关联，不显示处理状态，避免不必要的UI更新
      // 使用现有的聚焦文件方法实现自动关联
      chatHistoryService.focusCurrentEditingFile(fileId)
        .then(() => {
          console.log('✅ 已自动关联文件:', fileName)
          // 🔧 移除状态更新，避免触发重新渲染
        })
        .catch(error => {
          console.error('❌ 自动关联文件失败:', error)
          // 🔧 只在控制台记录错误，不更新UI状态
        })
    }
  }, [editorState.currentFile?.id, autoAssociationEnabled, chatHistoryService]) // 🔧 只依赖文件ID，避免不必要的重新执行

  // 处理自动关联开关变化
  const handleAutoAssociationToggle = (enabled: boolean) => {
    setAutoAssociationEnabled(enabled)
    console.log('🎛️ 自动关联功能', enabled ? '开启' : '关闭')
  }

  // 加载保存的编辑器设置
  const loadSavedSettings = async () => {
    try {
      const { DatabaseService } = await import('@/services/database/DatabaseService')
      const dbService = DatabaseService.getInstance()
      
      // 尝试获取保存的设置
      const result = await dbService.get('editor-settings', artworkId)
      
      if (result.success && result.data && result.data.settings) {
        // 更新编辑器状态
        setEditorState(prev => ({
          ...prev,
          settings: result.data.settings
        }))
        console.log('✅ 已加载保存的编辑器设置:', result.data.settings)
      } else {
        console.log('📝 未找到保存的设置，使用默认设置')
      }
    } catch (error) {
      console.error('❌ 加载编辑器设置失败:', error)
      // 使用默认设置
    }
  }

  // 🔧 智能文件内容更新事件监听 - 只响应外部工具更新
  useEffect(() => {
    const fileTreeEventService = FileTreeEventService.getInstance()
    
    const handleFileContentUpdate = async (updatedFileId: string) => {
      console.log('🔔 收到文件内容更新事件:', updatedFileId)
      
      // 如果更新的文件是当前打开的文件，重新加载文件内容
      if (editorState.currentFile && editorState.currentFile.id === updatedFileId) {
        console.log('🔄 当前文件内容已被外部工具更新，重新加载编辑器内容')
        
        try {
          const fileTreeService = FileTreeService.getInstance()
          const result = await fileTreeService.getFile(updatedFileId)
          
          if (result.success && result.data) {
            const updatedEditorFile: EditorFile = {
              id: result.data.id,
              name: result.data.name,
              content: result.data.content,
              type: result.data.type,
              path: result.data.path,
              lastModified: result.data.updatedAt,
              isDirty: false,
              isReadOnly: false
            }

            setEditorState(prev => ({
              ...prev,
              currentFile: updatedEditorFile,
              unsavedChanges: false
            }))

            console.log('✅ 编辑器内容已自动更新（外部工具）:', result.data.name)
          }
        } catch (error) {
          console.error('❌ 重新加载文件内容失败:', error)
        }
      }
    }
    
    // 订阅文件内容更新事件
    fileTreeEventService.subscribeFileContentUpdate(handleFileContentUpdate)
    console.log('✅ 已订阅文件内容更新事件（仅外部工具）')
    
    return () => {
      // 组件卸载时清理事件监听器
      fileTreeEventService.unsubscribeFileContentUpdate(handleFileContentUpdate)
      console.log('🧹 已清理文件内容更新事件监听器')
    }
  }, [editorState.currentFile?.id]) // 依赖当前文件ID，确保监听器能正确判断

  // 加载作品数据
  useEffect(() => {
    const loadArtwork = async () => {
      if (!artworkId) {
        setEditorState(prev => ({
          ...prev,
          isLoading: false,
          error: '无效的作品ID'
        }))
        return
      }

      try {
        const artworkService = ArtworkService.getInstance()
        const result = await artworkService.getArtwork(artworkId)

        if (result.success && result.data) {
          setArtwork(result.data)
          setEditorState(prev => ({
            ...prev,
            isLoading: false,
            error: null
          }))
          
          // 加载保存的设置
          await loadSavedSettings()
          
          // 恢复字体应用状态（字体持久化功能）
          try {
            console.log('🔄 尝试恢复字体应用状态...')
            
            const { FontPersistenceService } = await import('@/services/fontPersistenceService')
            const persistenceService = FontPersistenceService.getInstance()
            
            // 延迟一点时间确保DOM完全加载
            await new Promise(resolve => setTimeout(resolve, 300))
            
            const restoreResult = await persistenceService.restoreFontApplication()
            if (restoreResult.success && restoreResult.data) {
              console.log('✅ 字体应用状态已恢复')
              
              // 获取当前应用的字体配置
              const configResult = await persistenceService.getActiveConfig()
              if (configResult.success && configResult.data) {
                const fontFamily = configResult.data.fontFamily
                console.log('🎯 当前应用的字体:', fontFamily)
                
                // 立即更新CSS变量
                document.documentElement.style.setProperty('--font-family-applied', `'${fontFamily}', sans-serif`)
                document.documentElement.style.setProperty('--font-family-handwritten', `'${fontFamily}', cursive, var(--font-family-primary)`)
                
                // 强制应用到所有元素
                const applyFontToAllElements = () => {
                  // 应用到body
                  document.body.style.setProperty('font-family', `'${fontFamily}', sans-serif`, 'important')
                  
                  // 白名单方式：只对指定的元素应用用户字体
                  const whitelistSelectors = [
                    // 页面标题和导航
                    'header h1, header h2, header p',
                    'nav, nav *',
                    
                    // 文件树面板
                    '.file-tree, .file-tree *',
                    
                    // AI助手面板
                    '.ai-assistant, .ai-assistant *',
                    
                    // 按钮和交互元素（非Monaco Editor内）
                    '.btn:not(.monaco-editor .btn)',
                    'button:not(.monaco-editor button)',
                    
                    // 特定的字体应用类
                    '.font-applied, .font-handwritten, .font-primary',
                    '.handdrawn-text',
                    
                    // 表单元素（非编辑器内）
                    'input:not(.monaco-editor input)',
                    'textarea:not(.monaco-editor textarea)',
                    'label:not(.monaco-editor label)',
                    'select:not(.monaco-editor select)',
                    
                    // 编辑器工具栏和设置面板
                    '.editor-toolbar, .editor-toolbar *',
                    '.editor-settings, .editor-settings *',
                    
                    // 文件名和状态信息
                    '.file-name, .file-status',
                    
                    // 侧边栏内容
                    '.sidebar, .sidebar *',
                    '.panel-header, .panel-content'
                  ]
                  
                  whitelistSelectors.forEach(selector => {
                    try {
                      const elements = document.querySelectorAll(selector)
                      elements.forEach(element => {
                        (element as HTMLElement).style.setProperty('font-family', `'${fontFamily}', sans-serif`, 'important')
                      })
                    } catch (e) {
                      // 忽略选择器错误
                      console.warn('字体应用选择器错误:', selector, e)
                    }
                  })
                  
                  // 强制重排
                  document.body.offsetHeight
                }
                
                // 立即应用
                applyFontToAllElements()
                
                // 延迟再次应用，确保所有动态加载的元素也能应用字体
                setTimeout(applyFontToAllElements, 500)
                setTimeout(applyFontToAllElements, 1000)
              }
            } else {
              console.log('📝 没有需要恢复的字体配置')
            }
          } catch (fontError) {
            console.warn('⚠️ 字体恢复失败，但不影响编辑器启动:', fontError)
          }
          
          // 预加载所有字体，确保字体立即可用
          try {
            const { FontService } = await import('@/services/fontService')
            const fontService = FontService.getInstance()
            await fontService.preloadAllFonts()
            console.log('✅ 所有字体已预加载')
          } catch (error) {
            console.error('❌ 预加载字体失败:', error)
          }
        } else {
          setEditorState(prev => ({
            ...prev,
            isLoading: false,
            error: result.error || '作品不存在'
          }))
        }
      } catch (error) {
        console.error('加载作品失败:', error)
        setEditorState(prev => ({
          ...prev,
          isLoading: false,
          error: '加载作品失败'
        }))
      }
    }

    loadArtwork()
  }, [artworkId])

  // 返回主页
  const handleGoBack = () => {
    if (editorState.unsavedChanges) {
      const confirmed = window.confirm('您有未保存的更改，确定要离开吗？')
      if (!confirmed) return
    }
    router.push('/')
  }



  // 处理文件内容变化
  const handleContentChange = async (content: string) => {
    if (!editorState.currentFile) return

    // 🔧 获取当前文件ID作为基准，防止文件切换时的竞态条件
    const currentFileId = editorState.currentFile.id

    // 更新编辑器状态
    const updatedFile = {
      ...editorState.currentFile,
      content,
      isDirty: true,
      lastModified: Date.now()
    }

    setEditorState(prev => ({
      ...prev,
      currentFile: updatedFile,
      unsavedChanges: true
    }))

    // 🔧 短暂延迟确保状态同步，然后验证文件ID一致性
    await new Promise(resolve => setTimeout(resolve, 10))

    // 🔧 验证文件ID一致性，防止文件切换过程中的错误保存
    if (editorState.currentFile?.id !== currentFileId) {
      console.log('🔒 文件已切换，跳过自动保存，避免内容错乱')
      return
    }

    // 自动保存到数据库
    try {
      const fileTreeService = FileTreeService.getInstance()
      // 🔧 用户编辑时使用 'user' 来源，不会触发文件内容更新事件
      await fileTreeService.updateFileContent(updatedFile.id, content, 'user')

      // 保存成功后更新状态
      setEditorState(prev => ({
        ...prev,
        currentFile: { ...updatedFile, isDirty: false },
        unsavedChanges: false
      }))

      console.log('✅ 文件自动保存成功')
    } catch (error) {
      console.error('❌ 文件保存失败:', error)
    }
  }

  // 处理编辑器设置变化并持久化
  const handleSettingsChange = async (newSettings: EditorSettings) => {
    try {
      // 更新状态
      setEditorState(prev => ({
        ...prev,
        settings: newSettings
      }))
      
      // 持久化设置到IndexedDB
      const { DatabaseService } = await import('@/services/database/DatabaseService')
      const dbService = DatabaseService.getInstance()
      
      // 保存设置，使用artworkId作为键
      await dbService.put('editor-settings', {
        id: artworkId,
        settings: newSettings,
        updatedAt: Date.now()
      })
      
      console.log('✅ 编辑器设置已保存')
    } catch (error) {
      console.error('❌ 保存编辑器设置失败:', error)
    }
  }

  // 处理文件选择（从文件树）
  const handleFileSelect = async (file: FileTreeNode) => {
    if (file.type !== 'file') {
      console.log('选择的不是文件，跳过:', file.name)
      return
    }

    try {
      console.log('🔍 开始加载文件:', file.name)
      
      const fileTreeService = FileTreeService.getInstance()
      const fileResult = await fileTreeService.getFile(file.id)
      
      if (fileResult.success && fileResult.data) {
        const storedFile = fileResult.data
        
        // 将StoredFile转换为EditorFile
        const editorFile: EditorFile = {
          id: storedFile.id,
          name: storedFile.name,
          content: storedFile.content || '',
          type: storedFile.type,
          path: storedFile.path,
          lastModified: storedFile.updatedAt,
          isDirty: false,
          isReadOnly: false
        }
        
        // 更新编辑器状态
        setEditorState(prev => ({
          ...prev,
          currentFile: editorFile,
          unsavedChanges: false,
          error: null
        }))
        
        console.log('✅ 文件选择成功:', editorFile.name)
      } else {
        console.error('❌ 文件加载失败:', fileResult.error)
        setEditorState(prev => ({
          ...prev,
          error: fileResult.error || '文件加载失败'
        }))
      }
    } catch (error) {
      console.error('❌ 文件选择异常:', error)
      setEditorState(prev => ({
        ...prev,
        error: '文件选择时发生错误'
      }))
    }
  }

  // 根据文件ID查找文件节点的辅助函数
  const findFileById = (rootNode: FileTreeNode, targetId: string): FileTreeNode | null => {
    if (rootNode.id === targetId) {
      return rootNode
    }
    
    if (rootNode.children) {
      for (const child of rootNode.children) {
        const found = findFileById(child, targetId)
        if (found) {
          return found
        }
      }
    }
    
    return null
  }

  // 根据目录名查找目录节点的辅助函数
  const findDirectoryByName = (rootNode: FileTreeNode, dirName: string, parentId: string): FileTreeNode | null => {
    // 递归搜索函数
    const searchNode = (node: FileTreeNode): FileTreeNode | null => {
      // 检查是否是目标目录
      if (node.type === 'folder' && node.name === dirName && node.parentId === parentId) {
        return node
      }
      
      // 递归检查子节点
      if (node.children && Array.isArray(node.children)) {
        for (const child of node.children) {
          const found = searchNode(child)
          if (found) return found
        }
      }
      
      return null
    }
    
    return searchNode(rootNode)
  }

  // 根据文件路径查找文件节点的辅助函数
  const findFileByPath = (rootNode: FileTreeNode, targetPath: string): FileTreeNode | null => {
    // 增强的路径标准化函数
    const normalizePath = (path: string): string => {
      if (!path) return ''
      // 移除开头的斜杠，统一分隔符，但保持大小写（中文路径敏感）
      return path.replace(/^\/+/, '').replace(/\\/g, '/').replace(/\/+/g, '/')
    }
    
    const normalizedTarget = normalizePath(targetPath)
    
    // 递归搜索函数
    const searchNode = (node: FileTreeNode): FileTreeNode | null => {
      const normalizedNodePath = normalizePath(node.path || '')
      const normalizedNodeName = normalizePath(node.name || '')
      
      console.log('🔍 编辑器页面查找文件:', { 
        targetPath, 
        normalizedTarget,
        nodeName: node.name,
        nodePath: node.path,
        normalizedNodePath,
        normalizedNodeName,
        nodeType: node.type
      })
      
      // 🔧 排除根目录：根目录不应该被匹配为文件
      if (node.name === 'root' || node.path === '/' || normalizedNodePath === '') {
        console.log('⏭️ 跳过根目录，继续搜索子节点')
        // 直接搜索子节点，不匹配根目录本身
        if (node.children && Array.isArray(node.children)) {
          for (const child of node.children) {
            const found = searchNode(child)
            if (found) return found
          }
        }
        return null
      }
      
      // 🔧 只匹配文件类型的节点，排除文件夹
      const isFile = node.type === 'file' || (!node.children || node.children.length === 0)
      
      // 1. 精确路径匹配（仅限文件）
      if (isFile && normalizedNodePath === normalizedTarget) {
        console.log('✅ 编辑器页面精确路径匹配（文件）:', normalizedNodePath)
        return node
      }
      
      // 2. 文件名匹配（仅限文件）
      if (isFile && normalizedNodeName === normalizedTarget) {
        console.log('✅ 编辑器页面文件名匹配（文件）:', normalizedNodeName)
        return node
      }
      
      // 3. 路径末尾匹配（仅限文件）
      const targetParts = normalizedTarget.split('/').filter(p => p)
      const nodeParts = normalizedNodePath.split('/').filter(p => p)
      
      // 检查目标路径是否是节点路径的后缀
      if (isFile && targetParts.length <= nodeParts.length) {
        const nodePathSuffix = nodeParts.slice(-targetParts.length).join('/')
        if (nodePathSuffix === normalizedTarget) {
          console.log('✅ 编辑器页面路径后缀匹配（文件）:', { nodePathSuffix, normalizedTarget })
          return node
        }
      }
      
      // 4. 文件名部分匹配（仅限文件）
      const targetFileName = targetParts[targetParts.length - 1]
      if (isFile && targetFileName && normalizedNodeName === targetFileName) {
        console.log('✅ 编辑器页面文件名部分匹配（文件）:', { targetFileName, normalizedNodeName })
        return node
      }
      
      // 5. 模糊匹配（仅限文件）
      if (isFile && (normalizedNodePath.includes(normalizedTarget) || normalizedTarget.includes(normalizedNodePath))) {
        console.log('✅ 编辑器页面模糊路径匹配（文件）:', { normalizedNodePath, normalizedTarget })
        return node
      }
      
      // 递归检查子节点
      if (node.children && Array.isArray(node.children)) {
        for (const child of node.children) {
          const found = searchNode(child)
          if (found) return found
        }
      }
      
      return null
    }
    
    return searchNode(rootNode)
  }

  // 处理文件选择（通过文件ID，用于DF工具跳转）
  const handleFileSelectById = async (fileId: string) => {
    try {
      console.log('🔍 根据ID查找文件:', fileId)
      
      const fileTreeService = FileTreeService.getInstance()
      const fileTreeResult = await fileTreeService.getFileTree(artworkId)
      
      if (fileTreeResult.success && fileTreeResult.data) {
        const file = findFileById(fileTreeResult.data, fileId)
        if (file) {
          await handleFileSelect(file)
          console.log('✅ 通过ID选择文件成功:', file.name)
        } else {
          console.warn('⚠️ 未找到指定ID的文件:', fileId)
          setEditorState(prev => ({
            ...prev,
            error: '未找到指定的文件'
          }))
        }
      } else {
        console.error('❌ 获取文件树失败:', fileTreeResult.error)
        setEditorState(prev => ({
          ...prev,
          error: '获取文件树失败'
        }))
      }
    } catch (error) {
      console.error('❌ 通过ID选择文件异常:', error)
      setEditorState(prev => ({
        ...prev,
        error: '文件选择时发生错误'
      }))
    }
  }

  // 处理AI内容插入到编辑器
  const handleContentInsert = async (content: string, options: any) => {
    if (!editorState.currentFile) {
      console.warn('没有打开的文件，无法插入内容')
      return
    }

    try {
      const currentContent = editorState.currentFile.content
      let newContent = ''

      // 根据插入选项处理内容
      switch (options.position) {
        case 'start':
          newContent = content + (options.addNewlines ? '\n' : '') + currentContent
          break
        case 'end':
          newContent = currentContent + (options.addNewlines ? '\n' : '') + content
          break
        case 'replace':
          newContent = content
          break
        case 'cursor':
        default:
          // 对于光标位置，我们暂时插入到文件末尾
          // 在实际实现中，这里需要与Monaco编辑器集成获取光标位置
          newContent = currentContent + (options.addNewlines ? '\n' : '') + content
          break
      }

      // 更新文件内容
      await handleContentChange(newContent)
      
      console.log('✅ AI内容已插入到编辑器')
    } catch (error) {
      console.error('❌ 插入内容失败:', error)
      setEditorState(prev => ({
        ...prev,
        error: '插入内容失败'
      }))
    }
  }

  // EditorPanel的ref
  const editorPanelRef = useRef<any>(null)

  // 处理打开详细差异对比
  const handleOpenDetailedDiff = async (diffRequest: { filePath: string; operation: 'append' | 'replace'; content: string; previewMode?: boolean }) => {
    console.log('🔗 编辑页面收到详细对比请求:', diffRequest)
    
    try {
      // 1. 首先确保目标文件已打开
      if (!editorState.currentFile || editorState.currentFile.path !== diffRequest.filePath) {
        console.log('🔄 切换到目标文件:', diffRequest.filePath)
        
        // 根据文件路径查找并打开文件
        const fileTreeService = FileTreeService.getInstance()
        const fileTreeResult = await fileTreeService.getFileTree(artworkId)
        
        if (fileTreeResult.success && fileTreeResult.data) {
          const targetFile = findFileByPath(fileTreeResult.data, diffRequest.filePath)
          if (targetFile) {
            await handleFileSelect(targetFile)
            // 等待文件加载完成
            await new Promise(resolve => setTimeout(resolve, 100))
          } else {
            console.warn('⚠️ 未找到目标文件:', diffRequest.filePath)
            
            // 尝试创建文件
            if (diffRequest.operation === 'replace' || diffRequest.operation === 'append') {
              console.log('🔄 尝试创建文件:', diffRequest.filePath)
              
              // 解析文件路径，获取目录和文件名
              const pathParts = diffRequest.filePath.split('/')
              const fileName = pathParts.pop() || ''
              const dirPath = pathParts.join('/')
              
              try {
                // 首先确保目录存在
                let parentId = `${artworkId}-root` // 默认根目录
                
                // 如果有目录路径，需要创建或查找目录
                if (dirPath) {
                  const dirParts = dirPath.split('/').filter(p => p)
                  let currentParentId = `${artworkId}-root`
                  
                  // 逐级创建或查找目录
                  for (const dirName of dirParts) {
                    console.log('🔍 查找或创建目录:', dirName)
                    
                    // 重新获取文件树，查找是否已存在该目录
                    const updatedTreeResult = await fileTreeService.getFileTree(artworkId)
                    if (updatedTreeResult.success && updatedTreeResult.data) {
                      const existingDir = findDirectoryByName(updatedTreeResult.data, dirName, currentParentId)
                      
                      if (existingDir) {
                        console.log('✅ 找到已存在的目录:', dirName)
                        currentParentId = existingDir.id
                      } else {
                        console.log('🔄 创建新目录:', dirName)
                        const createDirResult = await fileTreeService.createFolder(artworkId, currentParentId, dirName)
                        
                        if (createDirResult.success && createDirResult.data) {
                          currentParentId = createDirResult.data.id
                          console.log('✅ 目录创建成功:', dirName)
                        } else {
                          throw new Error(`创建目录失败: ${createDirResult.error}`)
                        }
                      }
                    }
                  }
                  
                  parentId = currentParentId
                }
                
                // 创建文件
                console.log('🔄 创建文件:', { fileName, parentId })
                const createResult = await fileTreeService.createFile(artworkId, parentId, fileName, 'text', '')
                
                if (createResult.success && createResult.data) {
                  console.log('✅ 文件创建成功:', createResult.data)
                  
                  // 重新加载文件树并选择新创建的文件
                  const updatedTreeResult = await fileTreeService.getFileTree(artworkId)
                  if (updatedTreeResult.success && updatedTreeResult.data) {
                    const newFile = findFileByPath(updatedTreeResult.data, diffRequest.filePath)
                    if (newFile) {
                      await handleFileSelect(newFile)
                      await new Promise(resolve => setTimeout(resolve, 300))
                    }
                  }
                } else {
                  console.error('❌ 文件创建失败:', createResult.error)
                  return
                }
              } catch (createError) {
                console.error('❌ 文件创建异常:', createError)
                return
              }
            } else {
              return
            }
          }
        }
      }

      // 2. 调用EditorPanel的diff方法
      if (editorPanelRef.current && editorPanelRef.current.handleOpenDetailedDiff) {
        console.log('✅ 调用EditorPanel的diff方法')
        editorPanelRef.current.handleOpenDetailedDiff(diffRequest)
      } else {
        console.warn('⚠️ EditorPanel的diff方法不可用')
      }
    } catch (error) {
      console.error('❌ 打开详细差异对比失败:', error)
    }
  }

  // 处理创建文件/文件夹
  const handleCreateFile = async (type: 'file' | 'folder') => {
    try {
      const fileTreeService = FileTreeService.getInstance()

      // 在根目录下创建文件/文件夹
      const rootFolderId = `${artworkId}-root`
      
      // 生成唯一名称，避免重名冲突
      const baseName = type === 'file' ? '新文件' : '新文件夹'
      const extension = type === 'file' ? '.md' : ''
      let finalName = `${baseName}${extension}`
      let counter = 1
      
      // 检查是否存在同名文件，如果存在则添加数字后缀
      const existingFiles = await fileTreeService.getFileTree(artworkId)
      if (existingFiles.success && existingFiles.data?.children) {
        const existingNames = new Set(existingFiles.data.children.map(child => child.name))
        
        while (existingNames.has(finalName)) {
          finalName = `${baseName}${counter}${extension}`
          counter++
        }
      }

      if (type === 'file') {
        await fileTreeService.createFile(artworkId, rootFolderId, finalName, 'text')
      } else {
        await fileTreeService.createFolder(artworkId, rootFolderId, finalName)
      }

      console.log(`✅ 成功在根目录创建${type}:`, finalName)

      // 文件树会通过事件自动刷新，不再需要手动触发

    } catch (error) {
      console.error(`❌ 创建${type}失败:`, error)
      setEditorState(prev => ({
        ...prev,
        error: `创建${type}失败: ${error instanceof Error ? error.message : String(error)}`
      }))
    }
  }

  // 加载状态
  if (editorState.isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
        <div className="flex items-center gap-4 text-amber-400">
          <div className="w-8 h-8 border-3 border-amber-400 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-lg font-handwritten">加载编辑器中...</span>
        </div>
      </div>
    )
  }

  // 错误状态
  if (editorState.error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-lg bg-red-500/20 flex items-center justify-center">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" className="text-red-400">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
              <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2" />
              <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2" />
            </svg>
          </div>
          <div className="text-red-400 text-xl mb-6 font-handwritten">
            {editorState.error}
          </div>
          <button
            onClick={handleGoBack}
            className="px-6 py-3 bg-amber-500/20 hover:bg-amber-500/30 text-amber-400 hover:text-amber-300 border border-amber-500/50 rounded-lg transition-all duration-200 font-handwritten"
          >
            返回主页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black flex flex-col">
      {/* 页面头部 */}
      <header className="bg-gradient-to-r from-gray-900 to-gray-800 backdrop-blur-sm border-b border-amber-500/30 px-6 py-4 shadow-lg shadow-black/20 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={handleGoBack}
              className="p-2 rounded-lg hover:bg-amber-500/20 transition-colors text-amber-400 hover:text-amber-300"
              title="返回主页"
            >
              <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
            </button>
            <div>
              <h1 className="text-xl font-semibold text-amber-100 font-handwritten">
                {artwork?.title || '编辑作品'}
              </h1>
              {artwork?.description && (
                <p className="text-sm text-gray-300 mt-1">
                  {artwork.description}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {editorState.unsavedChanges && (
              <span className="text-sm text-amber-600 font-medium">
                未保存的更改
              </span>
            )}
            <div className="w-2 h-2 bg-green-400 rounded-full" title="已连接" />
          </div>
        </div>
      </header>

      {/* 主编辑区域 */}
      <main className="h-[calc(100vh-80px)] overflow-hidden">
        <ResizableLayout
          leftPanel={
            <div className="flex flex-col h-full">
              <div className="p-4 border-b border-amber-500/30 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <h2 className="text-sm font-semibold text-amber-100 font-handwritten">
                    文件管理
                  </h2>
                  <div className="flex items-center gap-1">
                    <button
                      className="p-1.5 rounded-md bg-amber-500/20 hover:bg-amber-500/30 transition-colors group"
                      title="新建文件"
                      onClick={() => handleCreateFile('file')}
                    >
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" className="text-amber-400">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        <line x1="12" y1="18" x2="12" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                        <line x1="9" y1="15" x2="15" y2="15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                      </svg>
                    </button>
                    <button
                      className="p-1.5 rounded-md bg-amber-500/20 hover:bg-amber-500/30 transition-colors group"
                      title="新建文件夹"
                      onClick={() => handleCreateFile('folder')}
                    >
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" className="text-amber-400">
                        <path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        <line x1="12" y1="14" x2="12" y2="10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                        <line x1="10" y1="12" x2="14" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <FileTreePanel
                  artworkId={artworkId}
                  currentFileId={editorState.currentFile?.id}
                  onFileSelect={handleFileSelect}
                  onFileCreate={(parentId, name, type) => {
                    console.log('创建文件:', { parentId, name, type })
                  }}
                  onFileDelete={(fileId) => {
                    console.log('删除文件:', fileId)
                  }}
                  onFileRename={(fileId, newName) => {
                    console.log('重命名文件:', { fileId, newName })
                  }}
                  className="h-full"
                />
              </div>
            </div>
          }
          centerPanel={
            <EditorPanel
              ref={editorPanelRef}
              file={editorState.currentFile}
              onContentChange={handleContentChange}
              onSettingsChange={handleSettingsChange}
              onFileRename={async (fileId, newName) => {
                try {
                  const fileTreeService = FileTreeService.getInstance()
                  await fileTreeService.renameFile(fileId, newName)
                  
                  // 更新当前文件状态
                  if (editorState.currentFile && editorState.currentFile.id === fileId) {
                    setEditorState(prev => ({
                      ...prev,
                      currentFile: {
                        ...prev.currentFile!,
                        name: newName
                      }
                    }))
                  }
                  
                  // 文件树会通过事件自动刷新，不再需要手动触发
                  
                  console.log('✅ 文件重命名成功:', newName)
                } catch (error) {
                  console.error('❌ 文件重命名失败:', error)
                  setEditorState(prev => ({
                    ...prev,
                    error: '文件重命名失败'
                  }))
                }
              }}
              settings={editorState.settings}
              onAutoAssociationToggle={handleAutoAssociationToggle}
              autoAssociationEnabled={autoAssociationEnabled}
              artworkId={artworkId}
              onOpenDetailedDiff={handleOpenDetailedDiff}
              className="h-full"
            />
          }
          rightPanel={
            <AIAssistantPanel
              onContentInsert={handleContentInsert}
              artworkId={artworkId}
              onFileSelect={handleFileSelectById}
              onOpenDetailedDiff={handleOpenDetailedDiff}
              className="h-full"
            />
          }
          initialLeftWidth={20}
          initialRightWidth={30}
          minLeftWidth={15}
          minRightWidth={20}
          minCenterWidth={30}
          onLayoutChange={(leftWidth, rightWidth, leftCollapsed, rightCollapsed) => {
            console.log('布局变化:', { leftWidth, rightWidth, leftCollapsed, rightCollapsed })
            // 通知 EditorPanel 处理布局变化
            if (editorPanelRef.current?.handleLayoutChange) {
              editorPanelRef.current.handleLayoutChange()
            }
          }}
        />
      </main>
    </div>
  )
}