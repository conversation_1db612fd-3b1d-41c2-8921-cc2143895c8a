/**
 * CoreManager - 核心管理器
 * 负责SimpleMindMap实例的创建、初始化和数据管理
 *
 * 职责：
 * - SimpleMindMap实例生命周期管理
 * - 数据设置和更新
 * - 基础配置管理
 */

import { createMutex } from 'lib0/mutex';
import { SimpleMindMapInstance, MindMapNode, MindMapConfig, DEFAULT_MINDMAP_CONFIG, EnhancedMindMapNode, MindMapDataMetadata } from '../types';

export interface CoreManagerConfig {
  container: HTMLElement;
  data: MindMapNode;
  readonly: boolean;
  config: Partial<MindMapConfig>;
}

export class CoreManager {
  private mindMapInstance: SimpleMindMapInstance | null = null;
  private config: CoreManagerConfig;
  private isInitialized = false;
  private initMutex = createMutex();

  // 数据版本控制
  private currentDataVersion: string | null = null;
  private instanceId: string = this.generateUniqueId();

  constructor(config: CoreManagerConfig) {
    this.config = config;
  }

  /**
   * 生成唯一标识符
   */
  private generateUniqueId(): string {
    return `mindmap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成数据版本号
   */
  private generateDataVersion(data: MindMapNode): string {
    const dataString = JSON.stringify(data);
    return `v_${Date.now()}_${this.hashCode(dataString)}`;
  }

  /**
   * 简单哈希函数
   */
  private hashCode(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 为数据添加元数据
   */
  private addDataMetadata(data: MindMapNode): EnhancedMindMapNode {
    const dataVersion = this.generateDataVersion(data);
    const enhancedData: EnhancedMindMapNode = {
      ...data,
      _metadata: {
        dataVersion,
        instanceId: this.instanceId,
        timestamp: Date.now()
      }
    };
    this.currentDataVersion = dataVersion;
    return enhancedData;
  }

  /**
   * 验证数据一致性
   */
  private validateDataConsistency(data: EnhancedMindMapNode): boolean {
    if (!data._metadata) {
      // 没有元数据的数据被认为是有效的（向后兼容）
      return true;
    }

    // 检查实例ID是否匹配
    if (data._metadata.instanceId !== this.instanceId) {
      console.warn('⚠️ 数据实例ID不匹配，可能来自不同的思维导图实例');
      return false;
    }

    return true;
  }

  /**
   * 移除数据元数据（用于传递给SimpleMindMap）
   */
  private removeDataMetadata(data: EnhancedMindMapNode): MindMapNode {
    const { _metadata, ...cleanData } = data;
    return cleanData as MindMapNode;
  }

  /**
   * 初始化SimpleMindMap实例
   */
  async initialize(): Promise<SimpleMindMapInstance> {
    return new Promise((resolve, reject) => {
      this.initMutex(async () => {
        try {
          // 动态导入SimpleMindMap完整版（包含所有插件，特别是Drag插件）
          const { default: SimpleMindMap } = await import('simple-mind-map/full.js') as any;

          // 清理现有实例（使用强制销毁）
          if (this.mindMapInstance) {
            await this.forceDestroy();
          }

          // 合并配置 - 不设置固定width/height，让SimpleMindMap自适应容器
          const finalConfig: any = {
            ...DEFAULT_MINDMAP_CONFIG,
            ...this.config.config,
            el: this.config.container,
            data: this.config.data,
            readonly: this.config.readonly
            // 移除固定的width/height设置，让SimpleMindMap自动适应容器尺寸
          };

          // 创建实例
          this.mindMapInstance = new SimpleMindMap(finalConfig) as unknown as SimpleMindMapInstance;
          this.isInitialized = true;
          resolve(this.mindMapInstance);

        } catch (error) {
          console.error('❌ SimpleMindMap初始化失败:', error);
          const errorMessage = `Failed to initialize SimpleMindMap: ${error instanceof Error ? error.message : 'Unknown error'}`;
          reject(new Error(errorMessage));
        }
      });
    });
  }

  /**
   * 获取SimpleMindMap实例
   */
  getInstance(): SimpleMindMapInstance | null {
    return this.mindMapInstance;
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized && this.mindMapInstance !== null;
  }

  /**
   * 设置数据（带版本控制）
   */
  setData(data: MindMapNode): void {
    if (!this.mindMapInstance) {
      throw new Error('MindMap instance not initialized');
    }

    try {
      // 添加数据元数据
      const enhancedData = this.addDataMetadata(data);

      // 验证数据一致性
      if (!this.validateDataConsistency(enhancedData)) {
        console.warn('⚠️ 数据版本不匹配，跳过设置');
        return;
      }

      // 移除元数据后传递给SimpleMindMap
      const cleanData = this.removeDataMetadata(enhancedData);
      this.mindMapInstance.setData(cleanData);

      console.log('✅ 数据设置成功，版本:', enhancedData._metadata?.dataVersion);
    } catch (error) {
      console.error('❌ 数据设置失败:', error);
      throw error;
    }
  }

  /**
   * 更新数据（性能优化版本）
   */
  updateData(data: MindMapNode): void {
    if (!this.mindMapInstance) {
      throw new Error('MindMap instance not initialized');
    }

    try {
      // 使用官方的updateData方法，性能更好
      if (typeof this.mindMapInstance.updateData === 'function') {
        this.mindMapInstance.updateData(data);
      } else {
        // 降级方案
        this.mindMapInstance.setData(data);
      }

    } catch (error) {
      console.error('❌ 数据更新失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据
   */
  getData(withConfig = false): any {
    if (!this.mindMapInstance) {
      throw new Error('MindMap instance not initialized');
    }

    try {
      return this.mindMapInstance.getData(withConfig);
    } catch (error) {
      console.error('❌ 获取数据失败:', error);
      throw error;
    }
  }

  /**
   * 容器尺寸变化后调整画布
   */
  resize(): void {
    if (!this.mindMapInstance) return;

    try {
      // 先更新容器位置和尺寸信息
      this.mindMapInstance.getElRectInfo();
      // 然后调整画布尺寸
      this.mindMapInstance.resize();

    } catch (error) {
      console.error('❌ 画布尺寸调整失败:', error);
    }
  }

  /**
   * 适应画布大小
   */
  fitView(): void {
    if (!this.mindMapInstance) return;

    try {
      // 使用官方API适应画布
      this.mindMapInstance.view.fit();
    } catch (error) {
      console.error('❌ 画布适应失败:', error);
    }
  }

  /**
   * 重置视图
   */
  resetView(): void {
    if (!this.mindMapInstance) return;

    try {
      this.mindMapInstance.view.reset();
    } catch (error) {
      console.error('❌ 视图重置失败:', error);
    }
  }

  /**
   * 坐标转换
   */
  toCanvasPosition(screenX: number, screenY: number): { x: number; y: number } {
    if (!this.mindMapInstance) {
      return { x: screenX, y: screenY };
    }

    try {
      return this.mindMapInstance.toPos(screenX, screenY);
    } catch (error) {
      console.error('❌ 坐标转换失败:', error);
      return { x: screenX, y: screenY };
    }
  }

  /**
   * 强制销毁实例（完整清理版本）
   */
  private async forceDestroy(): Promise<void> {
    if (!this.mindMapInstance) return;

    try {
      // 解绑所有事件监听器
      if (this.mindMapInstance) {
        try {
          // 尝试解绑常见的事件监听器
          const commonEvents = [
            'data_change', 'node_active', 'node_tree_render_end', 'set_data',
            'expand_btn_click', 'node_click', 'draw_click', 'svg_mousedown',
            'mousedown', 'mousemove', 'mouseup', 'contextmenu'
          ];

          commonEvents.forEach(eventName => {
            try {
              // 使用any类型避免类型检查问题
              const instance = this.mindMapInstance as any;
              if (instance && typeof instance.off === 'function') {
                instance.off(eventName, () => {});
              }
            } catch (error) {
              // 忽略解绑失败的事件
            }
          });
        } catch (error) {
          console.warn('⚠️ 事件解绑失败:', error);
        }
      }

      // 🔧 温和的实例销毁，避免DOM冲突
      try {
        // 只清理事件和状态，不调用可能导致DOM冲突的destroy
        if (typeof this.mindMapInstance.destroy === 'function') {
          this.mindMapInstance.destroy();
        }
      } catch (error) {
        console.warn('⚠️ SimpleMindMap销毁过程中出现错误，继续清理:', error);
      }

      // 🔧 跳过DOM清理，让React处理DOM管理
      // this.clearDOMReferences();

      // 等待销毁完成
      await this.waitForDestruction();

      console.log('✅ SimpleMindMap实例强制销毁完成');
    } catch (error) {
      console.error('❌ 强制销毁SimpleMindMap实例失败:', error);
      throw error;
    } finally {
      this.mindMapInstance = null;
      this.isInitialized = false;
    }
  }

  /**
   * 清理DOM引用
   */
  private clearDOMReferences(): void {
    try {
      // 🔧 温和的DOM清理，避免与React冲突
      if (this.config.container && this.config.container.parentNode) {
        const container = this.config.container;
        // 只清理SimpleMindMap创建的元素，避免移除React管理的DOM
        const simpleMindMapElements = container.querySelectorAll('.smm-container, .smm-svg, canvas');
        simpleMindMapElements.forEach(element => {
          try {
            if (element.parentNode === container) {
              container.removeChild(element);
            }
          } catch (e) {
            // 忽略已被移除的元素
          }
        });
      }
    } catch (error) {
      console.warn('⚠️ DOM引用清理失败:', error);
    }
  }

  /**
   * 等待销毁完成
   */
  private async waitForDestruction(): Promise<void> {
    // 给予适当的等待时间确保异步销毁完成
    return new Promise(resolve => {
      setTimeout(resolve, 100);
    });
  }

  /**
   * 销毁实例（保持向后兼容）
   */
  destroy(): void {
    if (this.mindMapInstance) {
      try {
        this.mindMapInstance.destroy();
        console.log('✅ SimpleMindMap实例销毁完成');
      } catch (error) {
        console.error('❌ 销毁SimpleMindMap实例失败:', error);
      }
    }

    this.mindMapInstance = null;
    this.isInitialized = false;
  }
}