"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/artwork/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts":
/*!****************************************************************!*\
  !*** ./src/components/MindMapRenderer/managers/CoreManager.ts ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreManager: function() { return /* binding */ CoreManager; }\n/* harmony export */ });\n/* harmony import */ var lib0_mutex__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/mutex */ \"(app-pages-browser)/./node_modules/lib0/mutex.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(app-pages-browser)/./src/components/MindMapRenderer/types/index.ts\");\n/**\r\n * CoreManager - 核心管理器\r\n * 负责SimpleMindMap实例的创建、初始化和数据管理\r\n *\r\n * 职责：\r\n * - SimpleMindMap实例生命周期管理\r\n * - 数据设置和更新\r\n * - 基础配置管理\r\n */ \n\nclass CoreManager {\n    /**\r\n   * 生成唯一标识符\r\n   */ generateUniqueId() {\n        return \"mindmap_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    /**\r\n   * 生成数据版本号\r\n   */ generateDataVersion(data) {\n        const dataString = JSON.stringify(data);\n        return \"v_\".concat(Date.now(), \"_\").concat(this.hashCode(dataString));\n    }\n    /**\r\n   * 简单哈希函数\r\n   */ hashCode(str) {\n        let hash = 0;\n        if (str.length === 0) return hash.toString();\n        for(let i = 0; i < str.length; i++){\n            const char = str.charCodeAt(i);\n            hash = (hash << 5) - hash + char;\n            hash = hash & hash; // Convert to 32bit integer\n        }\n        return Math.abs(hash).toString(36);\n    }\n    /**\r\n   * 为数据添加元数据\r\n   */ addDataMetadata(data) {\n        const dataVersion = this.generateDataVersion(data);\n        const enhancedData = {\n            ...data,\n            _metadata: {\n                dataVersion,\n                instanceId: this.instanceId,\n                timestamp: Date.now()\n            }\n        };\n        this.currentDataVersion = dataVersion;\n        return enhancedData;\n    }\n    /**\r\n   * 验证数据一致性\r\n   */ validateDataConsistency(data) {\n        if (!data._metadata) {\n            // 没有元数据的数据被认为是有效的（向后兼容）\n            return true;\n        }\n        // 检查实例ID是否匹配\n        if (data._metadata.instanceId !== this.instanceId) {\n            console.warn(\"⚠️ 数据实例ID不匹配，可能来自不同的思维导图实例\");\n            return false;\n        }\n        return true;\n    }\n    /**\r\n   * 移除数据元数据（用于传递给SimpleMindMap）\r\n   */ removeDataMetadata(data) {\n        const { _metadata, ...cleanData } = data;\n        return cleanData;\n    }\n    /**\r\n   * 初始化SimpleMindMap实例\r\n   */ async initialize() {\n        return new Promise((resolve, reject)=>{\n            this.initMutex(async ()=>{\n                try {\n                    // 动态导入SimpleMindMap完整版（包含所有插件，特别是Drag插件）\n                    const { default: SimpleMindMap } = await Promise.all(/*! import() */[__webpack_require__.e(\"css-node_modules_quill_dist_quill_snow_css\"), __webpack_require__.e(\"_app-pages-browser_node_modules_simple-mind-map_full_js\")]).then(__webpack_require__.bind(__webpack_require__, /*! simple-mind-map/full.js */ \"(app-pages-browser)/./node_modules/simple-mind-map/full.js\"));\n                    // 清理现有实例（使用强制销毁）\n                    if (this.mindMapInstance) {\n                        await this.forceDestroy();\n                    }\n                    // 合并配置 - 不设置固定width/height，让SimpleMindMap自适应容器\n                    const finalConfig = {\n                        ..._types__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MINDMAP_CONFIG,\n                        ...this.config.config,\n                        el: this.config.container,\n                        data: this.config.data,\n                        readonly: this.config.readonly\n                    };\n                    // 创建实例\n                    this.mindMapInstance = new SimpleMindMap(finalConfig);\n                    this.isInitialized = true;\n                    resolve(this.mindMapInstance);\n                } catch (error) {\n                    console.error(\"❌ SimpleMindMap初始化失败:\", error);\n                    const errorMessage = \"Failed to initialize SimpleMindMap: \".concat(error instanceof Error ? error.message : \"Unknown error\");\n                    reject(new Error(errorMessage));\n                }\n            });\n        });\n    }\n    /**\r\n   * 获取SimpleMindMap实例\r\n   */ getInstance() {\n        return this.mindMapInstance;\n    }\n    /**\r\n   * 检查是否已初始化\r\n   */ isReady() {\n        return this.isInitialized && this.mindMapInstance !== null;\n    }\n    /**\r\n   * 设置数据（带版本控制）\r\n   */ setData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            var _enhancedData__metadata;\n            // 添加数据元数据\n            const enhancedData = this.addDataMetadata(data);\n            // 验证数据一致性\n            if (!this.validateDataConsistency(enhancedData)) {\n                console.warn(\"⚠️ 数据版本不匹配，跳过设置\");\n                return;\n            }\n            // 移除元数据后传递给SimpleMindMap\n            const cleanData = this.removeDataMetadata(enhancedData);\n            this.mindMapInstance.setData(cleanData);\n            console.log(\"✅ 数据设置成功，版本:\", (_enhancedData__metadata = enhancedData._metadata) === null || _enhancedData__metadata === void 0 ? void 0 : _enhancedData__metadata.dataVersion);\n        } catch (error) {\n            console.error(\"❌ 数据设置失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 更新数据（性能优化版本）\r\n   */ updateData(data) {\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            // 使用官方的updateData方法，性能更好\n            if (typeof this.mindMapInstance.updateData === \"function\") {\n                this.mindMapInstance.updateData(data);\n            } else {\n                // 降级方案\n                this.mindMapInstance.setData(data);\n            }\n        } catch (error) {\n            console.error(\"❌ 数据更新失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 获取数据\r\n   */ getData() {\n        let withConfig = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!this.mindMapInstance) {\n            throw new Error(\"MindMap instance not initialized\");\n        }\n        try {\n            return this.mindMapInstance.getData(withConfig);\n        } catch (error) {\n            console.error(\"❌ 获取数据失败:\", error);\n            throw error;\n        }\n    }\n    /**\r\n   * 容器尺寸变化后调整画布\r\n   */ resize() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 先更新容器位置和尺寸信息\n            this.mindMapInstance.getElRectInfo();\n            // 然后调整画布尺寸\n            this.mindMapInstance.resize();\n        } catch (error) {\n            console.error(\"❌ 画布尺寸调整失败:\", error);\n        }\n    }\n    /**\r\n   * 适应画布大小\r\n   */ fitView() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 使用官方API适应画布\n            this.mindMapInstance.view.fit();\n        } catch (error) {\n            console.error(\"❌ 画布适应失败:\", error);\n        }\n    }\n    /**\r\n   * 重置视图\r\n   */ resetView() {\n        if (!this.mindMapInstance) return;\n        try {\n            this.mindMapInstance.view.reset();\n        } catch (error) {\n            console.error(\"❌ 视图重置失败:\", error);\n        }\n    }\n    /**\r\n   * 坐标转换\r\n   */ toCanvasPosition(screenX, screenY) {\n        if (!this.mindMapInstance) {\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n        try {\n            return this.mindMapInstance.toPos(screenX, screenY);\n        } catch (error) {\n            console.error(\"❌ 坐标转换失败:\", error);\n            return {\n                x: screenX,\n                y: screenY\n            };\n        }\n    }\n    /**\r\n   * 强制销毁实例（完整清理版本）\r\n   */ async forceDestroy() {\n        if (!this.mindMapInstance) return;\n        try {\n            // 解绑所有事件监听器\n            if (this.mindMapInstance) {\n                try {\n                    // 尝试解绑常见的事件监听器\n                    const commonEvents = [\n                        \"data_change\",\n                        \"node_active\",\n                        \"node_tree_render_end\",\n                        \"set_data\",\n                        \"expand_btn_click\",\n                        \"node_click\",\n                        \"draw_click\",\n                        \"svg_mousedown\",\n                        \"mousedown\",\n                        \"mousemove\",\n                        \"mouseup\",\n                        \"contextmenu\"\n                    ];\n                    commonEvents.forEach((eventName)=>{\n                        try {\n                            // 使用any类型避免类型检查问题\n                            const instance = this.mindMapInstance;\n                            if (instance && typeof instance.off === \"function\") {\n                                instance.off(eventName, ()=>{});\n                            }\n                        } catch (error) {\n                        // 忽略解绑失败的事件\n                        }\n                    });\n                } catch (error) {\n                    console.warn(\"⚠️ 事件解绑失败:\", error);\n                }\n            }\n            // 销毁实例\n            this.mindMapInstance.destroy();\n            // 清理DOM引用\n            this.clearDOMReferences();\n            // 等待销毁完成\n            await this.waitForDestruction();\n            console.log(\"✅ SimpleMindMap实例强制销毁完成\");\n        } catch (error) {\n            console.error(\"❌ 强制销毁SimpleMindMap实例失败:\", error);\n            throw error;\n        } finally{\n            this.mindMapInstance = null;\n            this.isInitialized = false;\n        }\n    }\n    /**\r\n   * 清理DOM引用\r\n   */ clearDOMReferences() {\n        try {\n            // 🔧 温和的DOM清理，避免与React冲突\n            if (this.config.container && this.config.container.parentNode) {\n                const container = this.config.container;\n                // 只清理SimpleMindMap创建的元素，避免移除React管理的DOM\n                const simpleMindMapElements = container.querySelectorAll(\".smm-container, .smm-svg, canvas\");\n                simpleMindMapElements.forEach((element)=>{\n                    try {\n                        if (element.parentNode === container) {\n                            container.removeChild(element);\n                        }\n                    } catch (e) {\n                    // 忽略已被移除的元素\n                    }\n                });\n            }\n        } catch (error) {\n            console.warn(\"⚠️ DOM引用清理失败:\", error);\n        }\n    }\n    /**\r\n   * 等待销毁完成\r\n   */ async waitForDestruction() {\n        // 给予适当的等待时间确保异步销毁完成\n        return new Promise((resolve)=>{\n            setTimeout(resolve, 100);\n        });\n    }\n    /**\r\n   * 销毁实例（保持向后兼容）\r\n   */ destroy() {\n        if (this.mindMapInstance) {\n            try {\n                this.mindMapInstance.destroy();\n                console.log(\"✅ SimpleMindMap实例销毁完成\");\n            } catch (error) {\n                console.error(\"❌ 销毁SimpleMindMap实例失败:\", error);\n            }\n        }\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n    }\n    constructor(config){\n        this.mindMapInstance = null;\n        this.isInitialized = false;\n        this.initMutex = (0,lib0_mutex__WEBPACK_IMPORTED_MODULE_1__.createMutex)();\n        // 数据版本控制\n        this.currentDataVersion = null;\n        this.instanceId = this.generateUniqueId();\n        this.config = config;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01pbmRNYXBSZW5kZXJlci9tYW5hZ2Vycy9Db3JlTWFuYWdlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Ozs7Q0FRQyxHQUV3QztBQUNzRztBQVN4SSxNQUFNRTtJQWNYOztHQUVDLEdBQ0QsbUJBQW1DO1FBQ2pDLE9BQU8sV0FBeUJFLE9BQWRDLEtBQUtDLEdBQUcsSUFBRyxLQUEyQyxPQUF4Q0YsS0FBS0csTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7SUFDdkU7SUFFQTs7R0FFQyxHQUNELG9CQUE0QkUsSUFBaUIsRUFBVTtRQUNyRCxNQUFNQyxhQUFhQyxLQUFLQyxTQUFTLENBQUNIO1FBQ2xDLE9BQU8sS0FBbUIsT0FBZE4sS0FBS0MsR0FBRyxJQUFHLEtBQTZCLE9BQTFCLElBQUksQ0FBQ1MsUUFBUSxDQUFDSDtJQUMxQztJQUVBOztHQUVDLEdBQ0QsU0FBaUJJLEdBQVcsRUFBVTtRQUNwQyxJQUFJQyxPQUFPO1FBQ1gsSUFBSUQsSUFBSUUsTUFBTSxLQUFLLEdBQUcsT0FBT0QsS0FBS1QsUUFBUTtRQUMxQyxJQUFLLElBQUlXLElBQUksR0FBR0EsSUFBSUgsSUFBSUUsTUFBTSxFQUFFQyxJQUFLO1lBQ25DLE1BQU1DLE9BQU9KLElBQUlLLFVBQVUsQ0FBQ0Y7WUFDNUJGLE9BQU8sQ0FBRUEsUUFBUSxLQUFLQSxPQUFRRztZQUM5QkgsT0FBT0EsT0FBT0EsTUFBTSwyQkFBMkI7UUFDakQ7UUFDQSxPQUFPYixLQUFLa0IsR0FBRyxDQUFDTCxNQUFNVCxRQUFRLENBQUM7SUFDakM7SUFFQTs7R0FFQyxHQUNELGdCQUF3QkcsSUFBaUIsRUFBdUI7UUFDOUQsTUFBTWEsY0FBYyxJQUFJLENBQUNkLG1CQUFtQixDQUFDQztRQUM3QyxNQUFNYyxlQUFvQztZQUN4QyxHQUFHZCxJQUFJO1lBQ1BlLFdBQVc7Z0JBQ1RGO2dCQUNBRyxZQUFZLElBQUksQ0FBQ0EsVUFBVTtnQkFDM0JDLFdBQVd2QixLQUFLQyxHQUFHO1lBQ3JCO1FBQ0Y7UUFDQSxJQUFJLENBQUN1QixrQkFBa0IsR0FBR0w7UUFDMUIsT0FBT0M7SUFDVDtJQUVBOztHQUVDLEdBQ0Qsd0JBQWdDZCxJQUF5QixFQUFXO1FBQ2xFLElBQUksQ0FBQ0EsS0FBS2UsU0FBUyxFQUFFO1lBQ25CLHdCQUF3QjtZQUN4QixPQUFPO1FBQ1Q7UUFFQSxhQUFhO1FBQ2IsSUFBSWYsS0FBS2UsU0FBUyxDQUFDQyxVQUFVLEtBQUssSUFBSSxDQUFDQSxVQUFVLEVBQUU7WUFDakRJLFFBQVFDLElBQUksQ0FBQztZQUNiLE9BQU87UUFDVDtRQUVBLE9BQU87SUFDVDtJQUVBOztHQUVDLEdBQ0QsbUJBQTJCckIsSUFBeUIsRUFBZTtRQUNqRSxNQUFNLEVBQUVlLFNBQVMsRUFBRSxHQUFHUSxXQUFXLEdBQUd2QjtRQUNwQyxPQUFPdUI7SUFDVDtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsYUFBNkM7UUFDakQsT0FBTyxJQUFJQyxRQUFRLENBQUNDLFNBQVNDO1lBQzNCLElBQUksQ0FBQ0MsU0FBUyxDQUFDO2dCQUNiLElBQUk7b0JBQ0YseUNBQXlDO29CQUN6QyxNQUFNLEVBQUVDLFNBQVNDLGFBQWEsRUFBRSxHQUFHLE1BQU0sb1VBQU87b0JBRWhELGlCQUFpQjtvQkFDakIsSUFBSSxJQUFJLENBQUNDLGVBQWUsRUFBRTt3QkFDeEIsTUFBTSxJQUFJLENBQUNDLFlBQVk7b0JBQ3pCO29CQUVBLCtDQUErQztvQkFDL0MsTUFBTUMsY0FBbUI7d0JBQ3ZCLEdBQUczQywwREFBc0I7d0JBQ3pCLEdBQUcsSUFBSSxDQUFDNEMsTUFBTSxDQUFDQSxNQUFNO3dCQUNyQkMsSUFBSSxJQUFJLENBQUNELE1BQU0sQ0FBQ0UsU0FBUzt3QkFDekJwQyxNQUFNLElBQUksQ0FBQ2tDLE1BQU0sQ0FBQ2xDLElBQUk7d0JBQ3RCcUMsVUFBVSxJQUFJLENBQUNILE1BQU0sQ0FBQ0csUUFBUTtvQkFFaEM7b0JBRUEsT0FBTztvQkFDUCxJQUFJLENBQUNOLGVBQWUsR0FBRyxJQUFJRCxjQUFjRztvQkFDekMsSUFBSSxDQUFDSyxhQUFhLEdBQUc7b0JBQ3JCWixRQUFRLElBQUksQ0FBQ0ssZUFBZTtnQkFFOUIsRUFBRSxPQUFPUSxPQUFPO29CQUNkbkIsUUFBUW1CLEtBQUssQ0FBQyx5QkFBeUJBO29CQUN2QyxNQUFNQyxlQUFlLHVDQUFnRyxPQUF6REQsaUJBQWlCRSxRQUFRRixNQUFNRyxPQUFPLEdBQUc7b0JBQ3JHZixPQUFPLElBQUljLE1BQU1EO2dCQUNuQjtZQUNGO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0RHLGNBQTRDO1FBQzFDLE9BQU8sSUFBSSxDQUFDWixlQUFlO0lBQzdCO0lBRUE7O0dBRUMsR0FDRGEsVUFBbUI7UUFDakIsT0FBTyxJQUFJLENBQUNOLGFBQWEsSUFBSSxJQUFJLENBQUNQLGVBQWUsS0FBSztJQUN4RDtJQUVBOztHQUVDLEdBQ0RjLFFBQVE3QyxJQUFpQixFQUFRO1FBQy9CLElBQUksQ0FBQyxJQUFJLENBQUMrQixlQUFlLEVBQUU7WUFDekIsTUFBTSxJQUFJVSxNQUFNO1FBQ2xCO1FBRUEsSUFBSTtnQkFjMEIzQjtZQWI1QixVQUFVO1lBQ1YsTUFBTUEsZUFBZSxJQUFJLENBQUNGLGVBQWUsQ0FBQ1o7WUFFMUMsVUFBVTtZQUNWLElBQUksQ0FBQyxJQUFJLENBQUNtQix1QkFBdUIsQ0FBQ0wsZUFBZTtnQkFDL0NNLFFBQVFDLElBQUksQ0FBQztnQkFDYjtZQUNGO1lBRUEseUJBQXlCO1lBQ3pCLE1BQU1FLFlBQVksSUFBSSxDQUFDRCxrQkFBa0IsQ0FBQ1I7WUFDMUMsSUFBSSxDQUFDaUIsZUFBZSxDQUFDYyxPQUFPLENBQUN0QjtZQUU3QkgsUUFBUTBCLEdBQUcsQ0FBQyxpQkFBZ0JoQywwQkFBQUEsYUFBYUMsU0FBUyxjQUF0QkQsOENBQUFBLHdCQUF3QkQsV0FBVztRQUNqRSxFQUFFLE9BQU8wQixPQUFPO1lBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0RRLFdBQVcvQyxJQUFpQixFQUFRO1FBQ2xDLElBQUksQ0FBQyxJQUFJLENBQUMrQixlQUFlLEVBQUU7WUFDekIsTUFBTSxJQUFJVSxNQUFNO1FBQ2xCO1FBRUEsSUFBSTtZQUNGLHlCQUF5QjtZQUN6QixJQUFJLE9BQU8sSUFBSSxDQUFDVixlQUFlLENBQUNnQixVQUFVLEtBQUssWUFBWTtnQkFDekQsSUFBSSxDQUFDaEIsZUFBZSxDQUFDZ0IsVUFBVSxDQUFDL0M7WUFDbEMsT0FBTztnQkFDTCxPQUFPO2dCQUNQLElBQUksQ0FBQytCLGVBQWUsQ0FBQ2MsT0FBTyxDQUFDN0M7WUFDL0I7UUFFRixFQUFFLE9BQU91QyxPQUFPO1lBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBOztHQUVDLEdBQ0RTLFVBQWlDO1lBQXpCQyxhQUFBQSxpRUFBYTtRQUNuQixJQUFJLENBQUMsSUFBSSxDQUFDbEIsZUFBZSxFQUFFO1lBQ3pCLE1BQU0sSUFBSVUsTUFBTTtRQUNsQjtRQUVBLElBQUk7WUFDRixPQUFPLElBQUksQ0FBQ1YsZUFBZSxDQUFDaUIsT0FBTyxDQUFDQztRQUN0QyxFQUFFLE9BQU9WLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMsYUFBYUE7WUFDM0IsTUFBTUE7UUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRFcsU0FBZTtRQUNiLElBQUksQ0FBQyxJQUFJLENBQUNuQixlQUFlLEVBQUU7UUFFM0IsSUFBSTtZQUNGLGVBQWU7WUFDZixJQUFJLENBQUNBLGVBQWUsQ0FBQ29CLGFBQWE7WUFDbEMsV0FBVztZQUNYLElBQUksQ0FBQ3BCLGVBQWUsQ0FBQ21CLE1BQU07UUFFN0IsRUFBRSxPQUFPWCxPQUFPO1lBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLGVBQWVBO1FBQy9CO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEYSxVQUFnQjtRQUNkLElBQUksQ0FBQyxJQUFJLENBQUNyQixlQUFlLEVBQUU7UUFFM0IsSUFBSTtZQUNGLGNBQWM7WUFDZCxJQUFJLENBQUNBLGVBQWUsQ0FBQ3NCLElBQUksQ0FBQ0MsR0FBRztRQUMvQixFQUFFLE9BQU9mLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMsYUFBYUE7UUFDN0I7SUFDRjtJQUVBOztHQUVDLEdBQ0RnQixZQUFrQjtRQUNoQixJQUFJLENBQUMsSUFBSSxDQUFDeEIsZUFBZSxFQUFFO1FBRTNCLElBQUk7WUFDRixJQUFJLENBQUNBLGVBQWUsQ0FBQ3NCLElBQUksQ0FBQ0csS0FBSztRQUNqQyxFQUFFLE9BQU9qQixPQUFPO1lBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLGFBQWFBO1FBQzdCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEa0IsaUJBQWlCQyxPQUFlLEVBQUVDLE9BQWUsRUFBNEI7UUFDM0UsSUFBSSxDQUFDLElBQUksQ0FBQzVCLGVBQWUsRUFBRTtZQUN6QixPQUFPO2dCQUFFNkIsR0FBR0Y7Z0JBQVNHLEdBQUdGO1lBQVE7UUFDbEM7UUFFQSxJQUFJO1lBQ0YsT0FBTyxJQUFJLENBQUM1QixlQUFlLENBQUMrQixLQUFLLENBQUNKLFNBQVNDO1FBQzdDLEVBQUUsT0FBT3BCLE9BQU87WUFDZG5CLFFBQVFtQixLQUFLLENBQUMsYUFBYUE7WUFDM0IsT0FBTztnQkFBRXFCLEdBQUdGO2dCQUFTRyxHQUFHRjtZQUFRO1FBQ2xDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWMzQixlQUE4QjtRQUMxQyxJQUFJLENBQUMsSUFBSSxDQUFDRCxlQUFlLEVBQUU7UUFFM0IsSUFBSTtZQUNGLFlBQVk7WUFDWixJQUFJLElBQUksQ0FBQ0EsZUFBZSxFQUFFO2dCQUN4QixJQUFJO29CQUNGLGVBQWU7b0JBQ2YsTUFBTWdDLGVBQWU7d0JBQ25CO3dCQUFlO3dCQUFlO3dCQUF3Qjt3QkFDdEQ7d0JBQW9CO3dCQUFjO3dCQUFjO3dCQUNoRDt3QkFBYTt3QkFBYTt3QkFBVztxQkFDdEM7b0JBRURBLGFBQWFDLE9BQU8sQ0FBQ0MsQ0FBQUE7d0JBQ25CLElBQUk7NEJBQ0Ysa0JBQWtCOzRCQUNsQixNQUFNQyxXQUFXLElBQUksQ0FBQ25DLGVBQWU7NEJBQ3JDLElBQUltQyxZQUFZLE9BQU9BLFNBQVNDLEdBQUcsS0FBSyxZQUFZO2dDQUNsREQsU0FBU0MsR0FBRyxDQUFDRixXQUFXLEtBQU87NEJBQ2pDO3dCQUNGLEVBQUUsT0FBTzFCLE9BQU87d0JBQ2QsWUFBWTt3QkFDZDtvQkFDRjtnQkFDRixFQUFFLE9BQU9BLE9BQU87b0JBQ2RuQixRQUFRQyxJQUFJLENBQUMsY0FBY2tCO2dCQUM3QjtZQUNGO1lBRUEsT0FBTztZQUNQLElBQUksQ0FBQ1IsZUFBZSxDQUFDcUMsT0FBTztZQUU1QixVQUFVO1lBQ1YsSUFBSSxDQUFDQyxrQkFBa0I7WUFFdkIsU0FBUztZQUNULE1BQU0sSUFBSSxDQUFDQyxrQkFBa0I7WUFFN0JsRCxRQUFRMEIsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPUCxPQUFPO1lBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUMsTUFBTUE7UUFDUixTQUFVO1lBQ1IsSUFBSSxDQUFDUixlQUFlLEdBQUc7WUFDdkIsSUFBSSxDQUFDTyxhQUFhLEdBQUc7UUFDdkI7SUFDRjtJQUVBOztHQUVDLEdBQ0QscUJBQW1DO1FBQ2pDLElBQUk7WUFDRix5QkFBeUI7WUFDekIsSUFBSSxJQUFJLENBQUNKLE1BQU0sQ0FBQ0UsU0FBUyxJQUFJLElBQUksQ0FBQ0YsTUFBTSxDQUFDRSxTQUFTLENBQUNtQyxVQUFVLEVBQUU7Z0JBQzdELE1BQU1uQyxZQUFZLElBQUksQ0FBQ0YsTUFBTSxDQUFDRSxTQUFTO2dCQUN2Qyx3Q0FBd0M7Z0JBQ3hDLE1BQU1vQyx3QkFBd0JwQyxVQUFVcUMsZ0JBQWdCLENBQUM7Z0JBQ3pERCxzQkFBc0JSLE9BQU8sQ0FBQ1UsQ0FBQUE7b0JBQzVCLElBQUk7d0JBQ0YsSUFBSUEsUUFBUUgsVUFBVSxLQUFLbkMsV0FBVzs0QkFDcENBLFVBQVV1QyxXQUFXLENBQUNEO3dCQUN4QjtvQkFDRixFQUFFLE9BQU9FLEdBQUc7b0JBQ1YsWUFBWTtvQkFDZDtnQkFDRjtZQUNGO1FBQ0YsRUFBRSxPQUFPckMsT0FBTztZQUNkbkIsUUFBUUMsSUFBSSxDQUFDLGlCQUFpQmtCO1FBQ2hDO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQWMrQixxQkFBb0M7UUFDaEQsb0JBQW9CO1FBQ3BCLE9BQU8sSUFBSTdDLFFBQVFDLENBQUFBO1lBQ2pCbUQsV0FBV25ELFNBQVM7UUFDdEI7SUFDRjtJQUVBOztHQUVDLEdBQ0QwQyxVQUFnQjtRQUNkLElBQUksSUFBSSxDQUFDckMsZUFBZSxFQUFFO1lBQ3hCLElBQUk7Z0JBQ0YsSUFBSSxDQUFDQSxlQUFlLENBQUNxQyxPQUFPO2dCQUM1QmhELFFBQVEwQixHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9QLE9BQU87Z0JBQ2RuQixRQUFRbUIsS0FBSyxDQUFDLDBCQUEwQkE7WUFDMUM7UUFDRjtRQUVBLElBQUksQ0FBQ1IsZUFBZSxHQUFHO1FBQ3ZCLElBQUksQ0FBQ08sYUFBYSxHQUFHO0lBQ3ZCO0lBdldBd0MsWUFBWTVDLE1BQXlCLENBQUU7YUFUL0JILGtCQUFnRDthQUVoRE8sZ0JBQWdCO2FBQ2hCVixZQUFZdkMsdURBQVdBO1FBRS9CLFNBQVM7YUFDRDZCLHFCQUFvQzthQUNwQ0YsYUFBcUIsSUFBSSxDQUFDeEIsZ0JBQWdCO1FBR2hELElBQUksQ0FBQzBDLE1BQU0sR0FBR0E7SUFDaEI7QUFzV0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvTWluZE1hcFJlbmRlcmVyL21hbmFnZXJzL0NvcmVNYW5hZ2VyLnRzPzI0NTUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIENvcmVNYW5hZ2VyIC0g5qC45b+D566h55CG5ZmoXHJcbiAqIOi0n+i0o1NpbXBsZU1pbmRNYXDlrp7kvovnmoTliJvlu7rjgIHliJ3lp4vljJblkozmlbDmja7nrqHnkIZcclxuICpcclxuICog6IGM6LSj77yaXHJcbiAqIC0gU2ltcGxlTWluZE1hcOWunuS+i+eUn+WRveWRqOacn+euoeeQhlxyXG4gKiAtIOaVsOaNruiuvue9ruWSjOabtOaWsFxyXG4gKiAtIOWfuuehgOmFjee9rueuoeeQhlxyXG4gKi9cclxuXHJcbmltcG9ydCB7IGNyZWF0ZU11dGV4IH0gZnJvbSAnbGliMC9tdXRleCc7XHJcbmltcG9ydCB7IFNpbXBsZU1pbmRNYXBJbnN0YW5jZSwgTWluZE1hcE5vZGUsIE1pbmRNYXBDb25maWcsIERFRkFVTFRfTUlORE1BUF9DT05GSUcsIEVuaGFuY2VkTWluZE1hcE5vZGUsIE1pbmRNYXBEYXRhTWV0YWRhdGEgfSBmcm9tICcuLi90eXBlcyc7XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIENvcmVNYW5hZ2VyQ29uZmlnIHtcclxuICBjb250YWluZXI6IEhUTUxFbGVtZW50O1xyXG4gIGRhdGE6IE1pbmRNYXBOb2RlO1xyXG4gIHJlYWRvbmx5OiBib29sZWFuO1xyXG4gIGNvbmZpZzogUGFydGlhbDxNaW5kTWFwQ29uZmlnPjtcclxufVxyXG5cclxuZXhwb3J0IGNsYXNzIENvcmVNYW5hZ2VyIHtcclxuICBwcml2YXRlIG1pbmRNYXBJbnN0YW5jZTogU2ltcGxlTWluZE1hcEluc3RhbmNlIHwgbnVsbCA9IG51bGw7XHJcbiAgcHJpdmF0ZSBjb25maWc6IENvcmVNYW5hZ2VyQ29uZmlnO1xyXG4gIHByaXZhdGUgaXNJbml0aWFsaXplZCA9IGZhbHNlO1xyXG4gIHByaXZhdGUgaW5pdE11dGV4ID0gY3JlYXRlTXV0ZXgoKTtcclxuXHJcbiAgLy8g5pWw5o2u54mI5pys5o6n5Yi2XHJcbiAgcHJpdmF0ZSBjdXJyZW50RGF0YVZlcnNpb246IHN0cmluZyB8IG51bGwgPSBudWxsO1xyXG4gIHByaXZhdGUgaW5zdGFuY2VJZDogc3RyaW5nID0gdGhpcy5nZW5lcmF0ZVVuaXF1ZUlkKCk7XHJcblxyXG4gIGNvbnN0cnVjdG9yKGNvbmZpZzogQ29yZU1hbmFnZXJDb25maWcpIHtcclxuICAgIHRoaXMuY29uZmlnID0gY29uZmlnO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog55Sf5oiQ5ZSv5LiA5qCH6K+G56ymXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBnZW5lcmF0ZVVuaXF1ZUlkKCk6IHN0cmluZyB7XHJcbiAgICByZXR1cm4gYG1pbmRtYXBfJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog55Sf5oiQ5pWw5o2u54mI5pys5Y+3XHJcbiAgICovXHJcbiAgcHJpdmF0ZSBnZW5lcmF0ZURhdGFWZXJzaW9uKGRhdGE6IE1pbmRNYXBOb2RlKTogc3RyaW5nIHtcclxuICAgIGNvbnN0IGRhdGFTdHJpbmcgPSBKU09OLnN0cmluZ2lmeShkYXRhKTtcclxuICAgIHJldHVybiBgdl8ke0RhdGUubm93KCl9XyR7dGhpcy5oYXNoQ29kZShkYXRhU3RyaW5nKX1gO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog566A5Y2V5ZOI5biM5Ye95pWwXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBoYXNoQ29kZShzdHI6IHN0cmluZyk6IHN0cmluZyB7XHJcbiAgICBsZXQgaGFzaCA9IDA7XHJcbiAgICBpZiAoc3RyLmxlbmd0aCA9PT0gMCkgcmV0dXJuIGhhc2gudG9TdHJpbmcoKTtcclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyLmxlbmd0aDsgaSsrKSB7XHJcbiAgICAgIGNvbnN0IGNoYXIgPSBzdHIuY2hhckNvZGVBdChpKTtcclxuICAgICAgaGFzaCA9ICgoaGFzaCA8PCA1KSAtIGhhc2gpICsgY2hhcjtcclxuICAgICAgaGFzaCA9IGhhc2ggJiBoYXNoOyAvLyBDb252ZXJ0IHRvIDMyYml0IGludGVnZXJcclxuICAgIH1cclxuICAgIHJldHVybiBNYXRoLmFicyhoYXNoKS50b1N0cmluZygzNik7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDkuLrmlbDmja7mt7vliqDlhYPmlbDmja5cclxuICAgKi9cclxuICBwcml2YXRlIGFkZERhdGFNZXRhZGF0YShkYXRhOiBNaW5kTWFwTm9kZSk6IEVuaGFuY2VkTWluZE1hcE5vZGUge1xyXG4gICAgY29uc3QgZGF0YVZlcnNpb24gPSB0aGlzLmdlbmVyYXRlRGF0YVZlcnNpb24oZGF0YSk7XHJcbiAgICBjb25zdCBlbmhhbmNlZERhdGE6IEVuaGFuY2VkTWluZE1hcE5vZGUgPSB7XHJcbiAgICAgIC4uLmRhdGEsXHJcbiAgICAgIF9tZXRhZGF0YToge1xyXG4gICAgICAgIGRhdGFWZXJzaW9uLFxyXG4gICAgICAgIGluc3RhbmNlSWQ6IHRoaXMuaW5zdGFuY2VJZCxcclxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcclxuICAgICAgfVxyXG4gICAgfTtcclxuICAgIHRoaXMuY3VycmVudERhdGFWZXJzaW9uID0gZGF0YVZlcnNpb247XHJcbiAgICByZXR1cm4gZW5oYW5jZWREYXRhO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6aqM6K+B5pWw5o2u5LiA6Ie05oCnXHJcbiAgICovXHJcbiAgcHJpdmF0ZSB2YWxpZGF0ZURhdGFDb25zaXN0ZW5jeShkYXRhOiBFbmhhbmNlZE1pbmRNYXBOb2RlKTogYm9vbGVhbiB7XHJcbiAgICBpZiAoIWRhdGEuX21ldGFkYXRhKSB7XHJcbiAgICAgIC8vIOayoeacieWFg+aVsOaNrueahOaVsOaNruiiq+iupOS4uuaYr+acieaViOeahO+8iOWQkeWQjuWFvOWuue+8iVxyXG4gICAgICByZXR1cm4gdHJ1ZTtcclxuICAgIH1cclxuXHJcbiAgICAvLyDmo4Dmn6Xlrp7kvotJROaYr+WQpuWMuemFjVxyXG4gICAgaWYgKGRhdGEuX21ldGFkYXRhLmluc3RhbmNlSWQgIT09IHRoaXMuaW5zdGFuY2VJZCkge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyDmlbDmja7lrp7kvotJROS4jeWMuemFje+8jOWPr+iDveadpeiHquS4jeWQjOeahOaAnee7tOWvvOWbvuWunuS+iycpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIHRydWU7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDnp7vpmaTmlbDmja7lhYPmlbDmja7vvIjnlKjkuo7kvKDpgJLnu5lTaW1wbGVNaW5kTWFw77yJXHJcbiAgICovXHJcbiAgcHJpdmF0ZSByZW1vdmVEYXRhTWV0YWRhdGEoZGF0YTogRW5oYW5jZWRNaW5kTWFwTm9kZSk6IE1pbmRNYXBOb2RlIHtcclxuICAgIGNvbnN0IHsgX21ldGFkYXRhLCAuLi5jbGVhbkRhdGEgfSA9IGRhdGE7XHJcbiAgICByZXR1cm4gY2xlYW5EYXRhIGFzIE1pbmRNYXBOb2RlO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5Yid5aeL5YyWU2ltcGxlTWluZE1hcOWunuS+i1xyXG4gICAqL1xyXG4gIGFzeW5jIGluaXRpYWxpemUoKTogUHJvbWlzZTxTaW1wbGVNaW5kTWFwSW5zdGFuY2U+IHtcclxuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XHJcbiAgICAgIHRoaXMuaW5pdE11dGV4KGFzeW5jICgpID0+IHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgLy8g5Yqo5oCB5a+85YWlU2ltcGxlTWluZE1hcOWujOaVtOeJiO+8iOWMheWQq+aJgOacieaPkuS7tu+8jOeJueWIq+aYr0RyYWfmj5Lku7bvvIlcclxuICAgICAgICAgIGNvbnN0IHsgZGVmYXVsdDogU2ltcGxlTWluZE1hcCB9ID0gYXdhaXQgaW1wb3J0KCdzaW1wbGUtbWluZC1tYXAvZnVsbC5qcycpIGFzIGFueTtcclxuXHJcbiAgICAgICAgICAvLyDmuIXnkIbnjrDmnInlrp7kvovvvIjkvb/nlKjlvLrliLbplIDmr4HvvIlcclxuICAgICAgICAgIGlmICh0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICAgICAgICBhd2FpdCB0aGlzLmZvcmNlRGVzdHJveSgpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIOWQiOW5tumFjee9riAtIOS4jeiuvue9ruWbuuWumndpZHRoL2hlaWdodO+8jOiuqVNpbXBsZU1pbmRNYXDoh6rpgILlupTlrrnlmahcclxuICAgICAgICAgIGNvbnN0IGZpbmFsQ29uZmlnOiBhbnkgPSB7XHJcbiAgICAgICAgICAgIC4uLkRFRkFVTFRfTUlORE1BUF9DT05GSUcsXHJcbiAgICAgICAgICAgIC4uLnRoaXMuY29uZmlnLmNvbmZpZyxcclxuICAgICAgICAgICAgZWw6IHRoaXMuY29uZmlnLmNvbnRhaW5lcixcclxuICAgICAgICAgICAgZGF0YTogdGhpcy5jb25maWcuZGF0YSxcclxuICAgICAgICAgICAgcmVhZG9ubHk6IHRoaXMuY29uZmlnLnJlYWRvbmx5XHJcbiAgICAgICAgICAgIC8vIOenu+mZpOWbuuWumueahHdpZHRoL2hlaWdodOiuvue9ru+8jOiuqVNpbXBsZU1pbmRNYXDoh6rliqjpgILlupTlrrnlmajlsLrlr7hcclxuICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgLy8g5Yib5bu65a6e5L6LXHJcbiAgICAgICAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZSA9IG5ldyBTaW1wbGVNaW5kTWFwKGZpbmFsQ29uZmlnKSBhcyB1bmtub3duIGFzIFNpbXBsZU1pbmRNYXBJbnN0YW5jZTtcclxuICAgICAgICAgIHRoaXMuaXNJbml0aWFsaXplZCA9IHRydWU7XHJcbiAgICAgICAgICByZXNvbHZlKHRoaXMubWluZE1hcEluc3RhbmNlKTtcclxuXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBTaW1wbGVNaW5kTWFw5Yid5aeL5YyW5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGBGYWlsZWQgdG8gaW5pdGlhbGl6ZSBTaW1wbGVNaW5kTWFwOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWA7XHJcbiAgICAgICAgICByZWplY3QobmV3IEVycm9yKGVycm9yTWVzc2FnZSkpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOiOt+WPllNpbXBsZU1pbmRNYXDlrp7kvotcclxuICAgKi9cclxuICBnZXRJbnN0YW5jZSgpOiBTaW1wbGVNaW5kTWFwSW5zdGFuY2UgfCBudWxsIHtcclxuICAgIHJldHVybiB0aGlzLm1pbmRNYXBJbnN0YW5jZTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOajgOafpeaYr+WQpuW3suWIneWni+WMllxyXG4gICAqL1xyXG4gIGlzUmVhZHkoKTogYm9vbGVhbiB7XHJcbiAgICByZXR1cm4gdGhpcy5pc0luaXRpYWxpemVkICYmIHRoaXMubWluZE1hcEluc3RhbmNlICE9PSBudWxsO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6K6+572u5pWw5o2u77yI5bim54mI5pys5o6n5Yi277yJXHJcbiAgICovXHJcbiAgc2V0RGF0YShkYXRhOiBNaW5kTWFwTm9kZSk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ01pbmRNYXAgaW5zdGFuY2Ugbm90IGluaXRpYWxpemVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g5re75Yqg5pWw5o2u5YWD5pWw5o2uXHJcbiAgICAgIGNvbnN0IGVuaGFuY2VkRGF0YSA9IHRoaXMuYWRkRGF0YU1ldGFkYXRhKGRhdGEpO1xyXG5cclxuICAgICAgLy8g6aqM6K+B5pWw5o2u5LiA6Ie05oCnXHJcbiAgICAgIGlmICghdGhpcy52YWxpZGF0ZURhdGFDb25zaXN0ZW5jeShlbmhhbmNlZERhdGEpKSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8g5pWw5o2u54mI5pys5LiN5Yy56YWN77yM6Lez6L+H6K6+572uJyk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyDnp7vpmaTlhYPmlbDmja7lkI7kvKDpgJLnu5lTaW1wbGVNaW5kTWFwXHJcbiAgICAgIGNvbnN0IGNsZWFuRGF0YSA9IHRoaXMucmVtb3ZlRGF0YU1ldGFkYXRhKGVuaGFuY2VkRGF0YSk7XHJcbiAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLnNldERhdGEoY2xlYW5EYXRhKTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCfinIUg5pWw5o2u6K6+572u5oiQ5Yqf77yM54mI5pysOicsIGVuaGFuY2VkRGF0YS5fbWV0YWRhdGE/LmRhdGFWZXJzaW9uKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDmlbDmja7orr7nva7lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOabtOaWsOaVsOaNru+8iOaAp+iDveS8mOWMlueJiOacrO+8iVxyXG4gICAqL1xyXG4gIHVwZGF0ZURhdGEoZGF0YTogTWluZE1hcE5vZGUpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy5taW5kTWFwSW5zdGFuY2UpIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdNaW5kTWFwIGluc3RhbmNlIG5vdCBpbml0aWFsaXplZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIOS9v+eUqOWumOaWueeahHVwZGF0ZURhdGHmlrnms5XvvIzmgKfog73mm7Tlpb1cclxuICAgICAgaWYgKHR5cGVvZiB0aGlzLm1pbmRNYXBJbnN0YW5jZS51cGRhdGVEYXRhID09PSAnZnVuY3Rpb24nKSB7XHJcbiAgICAgICAgdGhpcy5taW5kTWFwSW5zdGFuY2UudXBkYXRlRGF0YShkYXRhKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICAvLyDpmY3nuqfmlrnmoYhcclxuICAgICAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS5zZXREYXRhKGRhdGEpO1xyXG4gICAgICB9XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIOaVsOaNruabtOaWsOWksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6I635Y+W5pWw5o2uXHJcbiAgICovXHJcbiAgZ2V0RGF0YSh3aXRoQ29uZmlnID0gZmFsc2UpOiBhbnkge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ01pbmRNYXAgaW5zdGFuY2Ugbm90IGluaXRpYWxpemVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgcmV0dXJuIHRoaXMubWluZE1hcEluc3RhbmNlLmdldERhdGEod2l0aENvbmZpZyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W5pWw5o2u5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDlrrnlmajlsLrlr7jlj5jljJblkI7osIPmlbTnlLvluINcclxuICAgKi9cclxuICByZXNpemUoKTogdm9pZCB7XHJcbiAgICBpZiAoIXRoaXMubWluZE1hcEluc3RhbmNlKSByZXR1cm47XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g5YWI5pu05paw5a655Zmo5L2N572u5ZKM5bC65a+45L+h5oGvXHJcbiAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLmdldEVsUmVjdEluZm8oKTtcclxuICAgICAgLy8g54S25ZCO6LCD5pW055S75biD5bC65a+4XHJcbiAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLnJlc2l6ZSgpO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDnlLvluIPlsLrlr7josIPmlbTlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6YCC5bqU55S75biD5aSn5bCPXHJcbiAgICovXHJcbiAgZml0VmlldygpOiB2b2lkIHtcclxuICAgIGlmICghdGhpcy5taW5kTWFwSW5zdGFuY2UpIHJldHVybjtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAvLyDkvb/nlKjlrpjmlrlBUEnpgILlupTnlLvluINcclxuICAgICAgdGhpcy5taW5kTWFwSW5zdGFuY2Uudmlldy5maXQoKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDnlLvluIPpgILlupTlpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog6YeN572u6KeG5Zu+XHJcbiAgICovXHJcbiAgcmVzZXRWaWV3KCk6IHZvaWQge1xyXG4gICAgaWYgKCF0aGlzLm1pbmRNYXBJbnN0YW5jZSkgcmV0dXJuO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIHRoaXMubWluZE1hcEluc3RhbmNlLnZpZXcucmVzZXQoKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDop4blm77ph43nva7lpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5Z2Q5qCH6L2s5o2iXHJcbiAgICovXHJcbiAgdG9DYW52YXNQb3NpdGlvbihzY3JlZW5YOiBudW1iZXIsIHNjcmVlblk6IG51bWJlcik6IHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfSB7XHJcbiAgICBpZiAoIXRoaXMubWluZE1hcEluc3RhbmNlKSB7XHJcbiAgICAgIHJldHVybiB7IHg6IHNjcmVlblgsIHk6IHNjcmVlblkgfTtcclxuICAgIH1cclxuXHJcbiAgICB0cnkge1xyXG4gICAgICByZXR1cm4gdGhpcy5taW5kTWFwSW5zdGFuY2UudG9Qb3Moc2NyZWVuWCwgc2NyZWVuWSk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Z2Q5qCH6L2s5o2i5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgcmV0dXJuIHsgeDogc2NyZWVuWCwgeTogc2NyZWVuWSB9O1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog5by65Yi26ZSA5q+B5a6e5L6L77yI5a6M5pW05riF55CG54mI5pys77yJXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyBmb3JjZURlc3Ryb3koKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICBpZiAoIXRoaXMubWluZE1hcEluc3RhbmNlKSByZXR1cm47XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g6Kej57uR5omA5pyJ5LqL5Lu255uR5ZCs5ZmoXHJcbiAgICAgIGlmICh0aGlzLm1pbmRNYXBJbnN0YW5jZSkge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAvLyDlsJ3or5Xop6Pnu5HluLjop4HnmoTkuovku7bnm5HlkKzlmahcclxuICAgICAgICAgIGNvbnN0IGNvbW1vbkV2ZW50cyA9IFtcclxuICAgICAgICAgICAgJ2RhdGFfY2hhbmdlJywgJ25vZGVfYWN0aXZlJywgJ25vZGVfdHJlZV9yZW5kZXJfZW5kJywgJ3NldF9kYXRhJyxcclxuICAgICAgICAgICAgJ2V4cGFuZF9idG5fY2xpY2snLCAnbm9kZV9jbGljaycsICdkcmF3X2NsaWNrJywgJ3N2Z19tb3VzZWRvd24nLFxyXG4gICAgICAgICAgICAnbW91c2Vkb3duJywgJ21vdXNlbW92ZScsICdtb3VzZXVwJywgJ2NvbnRleHRtZW51J1xyXG4gICAgICAgICAgXTtcclxuXHJcbiAgICAgICAgICBjb21tb25FdmVudHMuZm9yRWFjaChldmVudE5hbWUgPT4ge1xyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgIC8vIOS9v+eUqGFueeexu+Wei+mBv+WFjeexu+Wei+ajgOafpemXrumimFxyXG4gICAgICAgICAgICAgIGNvbnN0IGluc3RhbmNlID0gdGhpcy5taW5kTWFwSW5zdGFuY2UgYXMgYW55O1xyXG4gICAgICAgICAgICAgIGlmIChpbnN0YW5jZSAmJiB0eXBlb2YgaW5zdGFuY2Uub2ZmID09PSAnZnVuY3Rpb24nKSB7XHJcbiAgICAgICAgICAgICAgICBpbnN0YW5jZS5vZmYoZXZlbnROYW1lLCAoKSA9PiB7fSk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAgIC8vIOW/veeVpeino+e7keWksei0peeahOS6i+S7tlxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8g5LqL5Lu26Kej57uR5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIOmUgOavgeWunuS+i1xyXG4gICAgICB0aGlzLm1pbmRNYXBJbnN0YW5jZS5kZXN0cm95KCk7XHJcblxyXG4gICAgICAvLyDmuIXnkIZET03lvJXnlKhcclxuICAgICAgdGhpcy5jbGVhckRPTVJlZmVyZW5jZXMoKTtcclxuXHJcbiAgICAgIC8vIOetieW+hemUgOavgeWujOaIkFxyXG4gICAgICBhd2FpdCB0aGlzLndhaXRGb3JEZXN0cnVjdGlvbigpO1xyXG5cclxuICAgICAgY29uc29sZS5sb2coJ+KchSBTaW1wbGVNaW5kTWFw5a6e5L6L5by65Yi26ZSA5q+B5a6M5oiQJyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5by65Yi26ZSA5q+BU2ltcGxlTWluZE1hcOWunuS+i+Wksei0pTonLCBlcnJvcik7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgdGhpcy5taW5kTWFwSW5zdGFuY2UgPSBudWxsO1xyXG4gICAgICB0aGlzLmlzSW5pdGlhbGl6ZWQgPSBmYWxzZTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIOa4heeQhkRPTeW8leeUqFxyXG4gICAqL1xyXG4gIHByaXZhdGUgY2xlYXJET01SZWZlcmVuY2VzKCk6IHZvaWQge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8g8J+UpyDmuKnlkoznmoRET03muIXnkIbvvIzpgb/lhY3kuI5SZWFjdOWGsueqgVxyXG4gICAgICBpZiAodGhpcy5jb25maWcuY29udGFpbmVyICYmIHRoaXMuY29uZmlnLmNvbnRhaW5lci5wYXJlbnROb2RlKSB7XHJcbiAgICAgICAgY29uc3QgY29udGFpbmVyID0gdGhpcy5jb25maWcuY29udGFpbmVyO1xyXG4gICAgICAgIC8vIOWPqua4heeQhlNpbXBsZU1pbmRNYXDliJvlu7rnmoTlhYPntKDvvIzpgb/lhY3np7vpmaRSZWFjdOeuoeeQhueahERPTVxyXG4gICAgICAgIGNvbnN0IHNpbXBsZU1pbmRNYXBFbGVtZW50cyA9IGNvbnRhaW5lci5xdWVyeVNlbGVjdG9yQWxsKCcuc21tLWNvbnRhaW5lciwgLnNtbS1zdmcsIGNhbnZhcycpO1xyXG4gICAgICAgIHNpbXBsZU1pbmRNYXBFbGVtZW50cy5mb3JFYWNoKGVsZW1lbnQgPT4ge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgaWYgKGVsZW1lbnQucGFyZW50Tm9kZSA9PT0gY29udGFpbmVyKSB7XHJcbiAgICAgICAgICAgICAgY29udGFpbmVyLnJlbW92ZUNoaWxkKGVsZW1lbnQpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9IGNhdGNoIChlKSB7XHJcbiAgICAgICAgICAgIC8vIOW/veeVpeW3suiiq+enu+mZpOeahOWFg+e0oFxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBET03lvJXnlKjmuIXnkIblpLHotKU6JywgZXJyb3IpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICog562J5b6F6ZSA5q+B5a6M5oiQXHJcbiAgICovXHJcbiAgcHJpdmF0ZSBhc3luYyB3YWl0Rm9yRGVzdHJ1Y3Rpb24oKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgICAvLyDnu5nkuojpgILlvZPnmoTnrYnlvoXml7bpl7Tnoa7kv53lvILmraXplIDmr4HlrozmiJBcclxuICAgIHJldHVybiBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHtcclxuICAgICAgc2V0VGltZW91dChyZXNvbHZlLCAxMDApO1xyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiDplIDmr4Hlrp7kvovvvIjkv53mjIHlkJHlkI7lhbzlrrnvvIlcclxuICAgKi9cclxuICBkZXN0cm95KCk6IHZvaWQge1xyXG4gICAgaWYgKHRoaXMubWluZE1hcEluc3RhbmNlKSB7XHJcbiAgICAgIHRyeSB7XHJcbiAgICAgICAgdGhpcy5taW5kTWFwSW5zdGFuY2UuZGVzdHJveSgpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgU2ltcGxlTWluZE1hcOWunuS+i+mUgOavgeWujOaIkCcpO1xyXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDplIDmr4FTaW1wbGVNaW5kTWFw5a6e5L6L5aSx6LSlOicsIGVycm9yKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHRoaXMubWluZE1hcEluc3RhbmNlID0gbnVsbDtcclxuICAgIHRoaXMuaXNJbml0aWFsaXplZCA9IGZhbHNlO1xyXG4gIH1cclxufSJdLCJuYW1lcyI6WyJjcmVhdGVNdXRleCIsIkRFRkFVTFRfTUlORE1BUF9DT05GSUciLCJDb3JlTWFuYWdlciIsImdlbmVyYXRlVW5pcXVlSWQiLCJNYXRoIiwiRGF0ZSIsIm5vdyIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwiZ2VuZXJhdGVEYXRhVmVyc2lvbiIsImRhdGEiLCJkYXRhU3RyaW5nIiwiSlNPTiIsInN0cmluZ2lmeSIsImhhc2hDb2RlIiwic3RyIiwiaGFzaCIsImxlbmd0aCIsImkiLCJjaGFyIiwiY2hhckNvZGVBdCIsImFicyIsImFkZERhdGFNZXRhZGF0YSIsImRhdGFWZXJzaW9uIiwiZW5oYW5jZWREYXRhIiwiX21ldGFkYXRhIiwiaW5zdGFuY2VJZCIsInRpbWVzdGFtcCIsImN1cnJlbnREYXRhVmVyc2lvbiIsInZhbGlkYXRlRGF0YUNvbnNpc3RlbmN5IiwiY29uc29sZSIsIndhcm4iLCJyZW1vdmVEYXRhTWV0YWRhdGEiLCJjbGVhbkRhdGEiLCJpbml0aWFsaXplIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJpbml0TXV0ZXgiLCJkZWZhdWx0IiwiU2ltcGxlTWluZE1hcCIsIm1pbmRNYXBJbnN0YW5jZSIsImZvcmNlRGVzdHJveSIsImZpbmFsQ29uZmlnIiwiY29uZmlnIiwiZWwiLCJjb250YWluZXIiLCJyZWFkb25seSIsImlzSW5pdGlhbGl6ZWQiLCJlcnJvciIsImVycm9yTWVzc2FnZSIsIkVycm9yIiwibWVzc2FnZSIsImdldEluc3RhbmNlIiwiaXNSZWFkeSIsInNldERhdGEiLCJsb2ciLCJ1cGRhdGVEYXRhIiwiZ2V0RGF0YSIsIndpdGhDb25maWciLCJyZXNpemUiLCJnZXRFbFJlY3RJbmZvIiwiZml0VmlldyIsInZpZXciLCJmaXQiLCJyZXNldFZpZXciLCJyZXNldCIsInRvQ2FudmFzUG9zaXRpb24iLCJzY3JlZW5YIiwic2NyZWVuWSIsIngiLCJ5IiwidG9Qb3MiLCJjb21tb25FdmVudHMiLCJmb3JFYWNoIiwiZXZlbnROYW1lIiwiaW5zdGFuY2UiLCJvZmYiLCJkZXN0cm95IiwiY2xlYXJET01SZWZlcmVuY2VzIiwid2FpdEZvckRlc3RydWN0aW9uIiwicGFyZW50Tm9kZSIsInNpbXBsZU1pbmRNYXBFbGVtZW50cyIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJlbGVtZW50IiwicmVtb3ZlQ2hpbGQiLCJlIiwic2V0VGltZW91dCIsImNvbnN0cnVjdG9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MindMapRenderer/managers/CoreManager.ts\n"));

/***/ })

});